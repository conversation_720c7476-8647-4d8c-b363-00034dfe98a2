{"/_not-found/page": "/_not-found", "/api/auth/active-mail/route": "/api/auth/active-mail", "/api/auth/active-authapp/route": "/api/auth/active-authapp", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/login/route": "/api/auth/login", "/api/auth/user/profile/route": "/api/auth/user/profile", "/api/auth/route": "/api/auth", "/api/uploads/media/[...path]/route": "/api/uploads/media/[...path]", "/api/pdf-proxy/route": "/api/pdf-proxy", "/api/revalidate/route": "/api/revalidate", "/api/auth/user/route": "/api/auth/user", "/api/uploads/single/[...path]/route": "/api/uploads/single/[...path]", "/api/auth/verify-authapp/route": "/api/auth/verify-authapp", "/robots.txt/route": "/robots.txt", "/(auth)/2fa/page": "/2fa", "/(auth)/register/page": "/register", "/(auth)/change-password/page": "/change-password", "/(auth)/forgot-pass/page": "/forgot-pass", "/(auth)/login/page": "/login", "/(auth)/logout/page": "/logout", "/(auth)/verify/page": "/verify", "/author/page": "/author", "/logout-direct/page": "/logout-direct", "/page": "/", "/(private)/dashboard/account/page": "/dashboard/account", "/(private)/dashboard/court-cases/custom-fields/page": "/dashboard/court-cases/custom-fields", "/(private)/dashboard/departments/[id]/page": "/dashboard/departments/[id]", "/(private)/dashboard/departments/[id]/members/[memberId]/edit/page": "/dashboard/departments/[id]/members/[memberId]/edit", "/(private)/dashboard/departments/[id]/edit/page": "/dashboard/departments/[id]/edit", "/(private)/dashboard/departments/[id]/members/add/page": "/dashboard/departments/[id]/members/add", "/(private)/dashboard/court-cases/page": "/dashboard/court-cases", "/(private)/dashboard/departments/page": "/dashboard/departments", "/(private)/dashboard/departments/[id]/members/[memberId]/page": "/dashboard/departments/[id]/members/[memberId]", "/(private)/dashboard/departments/add/page": "/dashboard/departments/add", "/(private)/dashboard/files/page": "/dashboard/files", "/(private)/dashboard/page": "/dashboard", "/(private)/dashboard/departments/[id]/members/page": "/dashboard/departments/[id]/members", "/(private)/dashboard/setting/page": "/dashboard/setting", "/(private)/dashboard/user/[id]/page": "/dashboard/user/[id]", "/(private)/dashboard/user/import/page": "/dashboard/user/import", "/(private)/dashboard/user/add/page": "/dashboard/user/add", "/(private)/dashboard/user/page": "/dashboard/user", "/(private)/dashboard/user/log/[id]/page": "/dashboard/user/log/[id]"}