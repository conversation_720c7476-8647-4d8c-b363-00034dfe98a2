{"/_not-found/page": "/_not-found", "/api/auth/login/route": "/api/auth/login", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/active-mail/route": "/api/auth/active-mail", "/api/auth/route": "/api/auth", "/api/pdf-proxy/route": "/api/pdf-proxy", "/api/auth/user/route": "/api/auth/user", "/api/auth/user/profile/route": "/api/auth/user/profile", "/api/auth/active-authapp/route": "/api/auth/active-authapp", "/api/uploads/media/[...path]/route": "/api/uploads/media/[...path]", "/api/auth/verify-authapp/route": "/api/auth/verify-authapp", "/api/uploads/single/[...path]/route": "/api/uploads/single/[...path]", "/robots.txt/route": "/robots.txt", "/api/revalidate/route": "/api/revalidate", "/(auth)/2fa/page": "/2fa", "/(auth)/logout/page": "/logout", "/(auth)/change-password/page": "/change-password", "/(auth)/verify/page": "/verify", "/(auth)/login/page": "/login", "/(auth)/register/page": "/register", "/(auth)/forgot-pass/page": "/forgot-pass", "/author/page": "/author", "/page": "/", "/logout-direct/page": "/logout-direct", "/(private)/dashboard/account/page": "/dashboard/account", "/(private)/dashboard/departments/[id]/members/add/page": "/dashboard/departments/[id]/members/add", "/(private)/dashboard/court-cases/custom-fields/page": "/dashboard/court-cases/custom-fields", "/(private)/dashboard/court-cases/page": "/dashboard/court-cases", "/(private)/dashboard/departments/[id]/edit/page": "/dashboard/departments/[id]/edit", "/(private)/dashboard/departments/[id]/page": "/dashboard/departments/[id]", "/(private)/dashboard/departments/[id]/members/[memberId]/edit/page": "/dashboard/departments/[id]/members/[memberId]/edit", "/(private)/dashboard/departments/page": "/dashboard/departments", "/(private)/dashboard/files/page": "/dashboard/files", "/(private)/dashboard/setting/page": "/dashboard/setting", "/(private)/dashboard/departments/[id]/members/page": "/dashboard/departments/[id]/members", "/(private)/dashboard/departments/[id]/members/[memberId]/page": "/dashboard/departments/[id]/members/[memberId]", "/(private)/dashboard/user/add/page": "/dashboard/user/add", "/(private)/dashboard/page": "/dashboard", "/(private)/dashboard/user/page": "/dashboard/user", "/(private)/dashboard/user/log/[id]/page": "/dashboard/user/log/[id]", "/(private)/dashboard/user/[id]/page": "/dashboard/user/[id]", "/(private)/dashboard/departments/add/page": "/dashboard/departments/add", "/(private)/dashboard/user/import/page": "/dashboard/user/import"}