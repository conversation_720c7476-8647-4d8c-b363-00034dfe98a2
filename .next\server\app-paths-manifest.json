{"/_not-found/page": "app/_not-found/page.js", "/api/auth/active-mail/route": "app/api/auth/active-mail/route.js", "/api/auth/active-authapp/route": "app/api/auth/active-authapp/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/user/profile/route": "app/api/auth/user/profile/route.js", "/api/auth/route": "app/api/auth/route.js", "/api/uploads/media/[...path]/route": "app/api/uploads/media/[...path]/route.js", "/api/pdf-proxy/route": "app/api/pdf-proxy/route.js", "/api/revalidate/route": "app/api/revalidate/route.js", "/api/auth/user/route": "app/api/auth/user/route.js", "/api/uploads/single/[...path]/route": "app/api/uploads/single/[...path]/route.js", "/api/auth/verify-authapp/route": "app/api/auth/verify-authapp/route.js", "/robots.txt/route": "app/robots.txt/route.js", "/(auth)/2fa/page": "app/(auth)/2fa/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(auth)/change-password/page": "app/(auth)/change-password/page.js", "/(auth)/forgot-pass/page": "app/(auth)/forgot-pass/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/logout/page": "app/(auth)/logout/page.js", "/(auth)/verify/page": "app/(auth)/verify/page.js", "/author/page": "app/author/page.js", "/logout-direct/page": "app/logout-direct/page.js", "/page": "app/page.js", "/(private)/dashboard/account/page": "app/(private)/dashboard/account/page.js", "/(private)/dashboard/court-cases/custom-fields/page": "app/(private)/dashboard/court-cases/custom-fields/page.js", "/(private)/dashboard/departments/[id]/page": "app/(private)/dashboard/departments/[id]/page.js", "/(private)/dashboard/departments/[id]/members/[memberId]/edit/page": "app/(private)/dashboard/departments/[id]/members/[memberId]/edit/page.js", "/(private)/dashboard/departments/[id]/edit/page": "app/(private)/dashboard/departments/[id]/edit/page.js", "/(private)/dashboard/departments/[id]/members/add/page": "app/(private)/dashboard/departments/[id]/members/add/page.js", "/(private)/dashboard/court-cases/page": "app/(private)/dashboard/court-cases/page.js", "/(private)/dashboard/departments/page": "app/(private)/dashboard/departments/page.js", "/(private)/dashboard/departments/[id]/members/[memberId]/page": "app/(private)/dashboard/departments/[id]/members/[memberId]/page.js", "/(private)/dashboard/departments/add/page": "app/(private)/dashboard/departments/add/page.js", "/(private)/dashboard/files/page": "app/(private)/dashboard/files/page.js", "/(private)/dashboard/page": "app/(private)/dashboard/page.js", "/(private)/dashboard/departments/[id]/members/page": "app/(private)/dashboard/departments/[id]/members/page.js", "/(private)/dashboard/setting/page": "app/(private)/dashboard/setting/page.js", "/(private)/dashboard/user/[id]/page": "app/(private)/dashboard/user/[id]/page.js", "/(private)/dashboard/user/import/page": "app/(private)/dashboard/user/import/page.js", "/(private)/dashboard/user/add/page": "app/(private)/dashboard/user/add/page.js", "/(private)/dashboard/user/page": "app/(private)/dashboard/user/page.js", "/(private)/dashboard/user/log/[id]/page": "app/(private)/dashboard/user/log/[id]/page.js"}