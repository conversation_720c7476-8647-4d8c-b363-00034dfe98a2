(()=>{var a={};a.id=2828,a.ids=[2828],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3368:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8545:(a,b,c)=>{Promise.resolve().then(c.bind(c,14405))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14405:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\court-cases\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx","default")},15303:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16945:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21697:(a,b,c)=>{Promise.resolve().then(c.bind(c,66995))},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31210:(a,b,c)=>{"use strict";c.d(b,{CN:()=>i,Lz:()=>j,Wu:()=>h,ZB:()=>g,Zp:()=>e,aR:()=>f});var d=c(21124);c(38301);let e=({children:a,className:b="",padding:c="md",shadow:e="sm",hover:f=!1,onClick:g})=>(0,d.jsx)("div",{className:`
        bg-white rounded-xl border border-gray-100
        ${{none:"",sm:"p-4",md:"p-6",lg:"p-8"}[c]}
        ${{none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[e]}
        ${f?"hover:shadow-lg transition-shadow duration-200":""}
        ${b}
      `,onClick:g,children:a}),f=({children:a,className:b=""})=>(0,d.jsx)("div",{className:`border-b border-gray-100 pb-4 mb-6 ${b}`,children:a}),g=({children:a,className:b="",size:c="md"})=>(0,d.jsx)("h2",{className:`font-bold text-gray-900 ${{sm:"text-lg",md:"text-xl",lg:"text-2xl"}[c]} ${b}`,children:a}),h=({children:a,className:b=""})=>(0,d.jsx)("div",{className:b,children:a}),i=({title:a,value:b,icon:c,trend:f,color:g="blue",onClick:h,clickable:i=!1})=>{let j=(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:a}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof b?b.toLocaleString():b}),f&&(0,d.jsxs)("p",{className:`text-sm mt-2 flex items-center ${f.isPositive?"text-green-600":"text-red-600"}`,children:[(0,d.jsx)("span",{className:"mr-1",children:f.isPositive?"↗":"↘"}),f.value]})]}),c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[g]}`,children:(0,d.jsx)("div",{className:"text-white",children:c})})]});return i&&h?(0,d.jsx)(e,{hover:!0,className:`relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ${i?"hover:shadow-lg":""}`,onClick:h,children:j}):(0,d.jsx)(e,{hover:!0,className:"relative overflow-hidden",children:j})},j=({title:a,description:b,icon:c,onClick:f,href:g,color:h="blue"})=>{let i=({children:a})=>(0,d.jsx)(e,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:a}),j=(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[c&&(0,d.jsx)("div",{className:`p-3 rounded-lg ${{blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[h]} flex-shrink-0`,children:(0,d.jsx)("div",{className:"text-white",children:c})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:a}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:b})]})]});return g?(0,d.jsx)("a",{href:g,children:(0,d.jsx)(i,{children:j})}):(0,d.jsx)("div",{onClick:f,children:(0,d.jsx)(i,{children:j})})}},33873:a=>{"use strict";a.exports=require("path")},34340:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["court-cases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14405)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,87473)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\court-cases\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/court-cases/page",pathname:"/dashboard/court-cases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/court-cases/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},40284:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43700:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},47089:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66995:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>R});var d=c(21124),e=c(38301),f=c.n(e),g=c(80974),h=c(65424);let i={getCourtCases:(a={})=>{let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,c.toString())});let c=b.toString(),d=c?`/api/court-cases?${c}`:"/api/court-cases";return h.Ay.get(d)},createCourtCase:a=>h.Ay.post("/api/court-cases",a),updateCourtCase:(a,b)=>h.Ay.put(`/api/court-cases/${a}`,b),deleteCourtCase:a=>h.Ay.delete(`/api/court-cases/${a}`),bulkDeleteCourtCases:a=>h.Ay.post("/api/court-cases/bulk-delete",{ids:a}),downloadTemplate:async()=>{let a=await fetch("/api/court-cases/template",{method:"GET",headers:{}});if(!a.ok)throw Error("Failed to download template");let b=await a.blob(),c=window.URL.createObjectURL(b),d=document.createElement("a");return d.href=c,d.download="mau-import-vu-viec-toa-an.xlsx",document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(c),{success:!0}},exportCourtCases:async(a={})=>{let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,c.toString())});let c=b.toString(),d=c?`/api/court-cases/export?${c}`:"/api/court-cases/export",e=await fetch(d,{method:"GET",headers:{}});if(!e.ok)throw Error("Failed to export data");return e.arrayBuffer()},previewImport:a=>{let b=new FormData;return b.append("file",a),h.Ay.post("/api/court-cases/preview-import",b)},importCourtCases:a=>{let b=new FormData;return b.append("file",a),h.Ay.post("/api/court-cases/import",b)}};var j=c(3368),k=c(76180),l=c(40284),m=c(77922),n=c(25816),o=c(72286),p=c(80517),q=c(90828),r=c(91292),s=c(16945),t=c(15303),u=c(75219);function v({date:a,warningDays:b=30,dangerDays:c=7,showIcon:f=!0,showText:g=!0,size:h="md",className:i=""}){let[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)("none"),n=()=>{if(null===j)return"";if(j<0){let a=Math.abs(j);return`Qu\xe1 hạn ${a} ng\xe0y`}let b=(a=>{if(!("string"==typeof a&&a.match(/^\d{2}\/\d{2}\/\d{4}$/)))return new Date(a);{let[b,c,d]=a.split("/");return new Date(parseInt(d),parseInt(c)-1,parseInt(b))}})(a),c=new Date,d=new Date(c.getTime()+6e4*c.getTimezoneOffset()+252e5);b.setHours(0,0,0,0),d.setHours(0,0,0,0);let e=b.getTime()-d.getTime();if(e>0){let a=Math.ceil(e/864e5);return`C\xf2n ${a} ng\xe0y`}return 0===j?"Hết hạn h\xf4m nay":1===j?"C\xf2n 1 ng\xe0y":`C\xf2n ${j} ng\xe0y`};if(!a||null===j)return null;let o={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:r.A,color:"text-red-600"},danger:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:s.A,color:"text-red-600"},warning:{bg:"bg-yellow-100",text:"text-yellow-800",border:"border-yellow-200",icon:t.A,color:"text-yellow-600"},safe:{bg:"bg-green-100",text:"text-green-800",border:"border-green-200",icon:u.A,color:"text-green-600"},none:{bg:"bg-gray-100",text:"text-gray-800",border:"border-gray-200",icon:t.A,color:"text-gray-600"}}[l],p={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-2.5 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-3 py-2 text-base",icon:16,gap:"gap-2"}}[h],q=o.icon;return(0,d.jsxs)("span",{className:`
        inline-flex items-center ${p.gap} ${p.container}
        ${o.bg} ${o.text} ${o.border}
        border rounded-full font-medium
        ${i}
      `,title:`${n()} - ${new Date(a).toLocaleDateString("vi-VN")}`,children:[f&&(0,d.jsx)(q,{size:p.icon,className:o.color}),g&&n()]})}let w=({cases:a,onCaseSelect:b,onCaseEdit:c,onCaseDelete:f,onBulkAction:h,onSort:i,currentSort:r,loading:s=!1})=>{let{hasPermission:t}=(0,n.S)(),[u,w]=(0,e.useState)([]),[x,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)([]),[B,C]=(0,e.useState)([]),[D,E]=(0,e.useState)(null),[F,G]=(0,e.useState)([]);(0,e.useEffect)(()=>{H();let a=setInterval(()=>{H()},3e4);return()=>clearInterval(a)},[]);let H=async()=>{try{let a=localStorage.getItem("sessionToken")||"",[b,c,d]=await Promise.all([o.A.getCustomFields("CourtCase",a),p.A.getFieldConfiguration("CourtCase",a),q.A.getDateCountdowns("CourtCase",a)]);if(b.payload.success&&c.payload.success){A(b.payload.fields),E(c.payload.configuration),d.payload.success&&G(d.payload.countdowns);let a=[];b.payload.fields.forEach(b=>{b.config.showInList&&a.push({...b})}),a.sort((a,b)=>a.config.sortOrder-b.config.sortOrder),C(a)}}catch(a){console.error("Error fetching custom fields:",a)}},I=({field:a,children:b})=>{let c=r?.sortBy===a,e=c&&r?.sortOrder==="asc",f=c&&r?.sortOrder==="desc";return(0,d.jsxs)("button",{onClick:()=>(a=>{if(!i)return;let b="asc";r?.sortBy===a&&(b="asc"===r.sortOrder?"desc":"asc"),i(a,b)})(a),className:"flex items-center gap-1 hover:text-gray-700 transition-colors",children:[b,(0,d.jsxs)("span",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:`text-xs leading-none ${e?"text-blue-600":"text-gray-400"}`,children:"▲"}),(0,d.jsx)("span",{className:`text-xs leading-none ${f?"text-blue-600":"text-gray-400"}`,children:"▼"})]})]})},J=(a,b)=>{let c=a.customFields?.[b.name];if(!c)return"";switch(b.dataType){case"date":return c?(0,m.Yq)(c):"";case"datetime":if(!c)return"";let d=new Date(c);if(isNaN(d.getTime()))return"";let e=d.getDate().toString().padStart(2,"0"),f=(d.getMonth()+1).toString().padStart(2,"0"),g=d.getFullYear().toString(),h=d.getHours().toString().padStart(2,"0"),i=d.getMinutes().toString().padStart(2,"0");return`${e}/${f}/${g} ${h}:${i}`;case"currency":return new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(c);case"percentage":return`${c}%`;case"boolean":return c?"C\xf3":"Kh\xf4ng";case"select":let j=b.config.options?.find(a=>a.value===c);return j?j.label:c;case"multiselect":if(Array.isArray(c))return c.map(a=>{let c=b.config.options?.find(b=>b.value===a);return c?c.label:a}).join(", ");return c;default:return c.toString()}};return s?(0,d.jsx)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:[...Array(8)].map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},b))})})}):(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:[(0,d.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch vụ việc t\xf2a \xe1n"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Hiển thị ",a?.length||0," vụ việc với ",B?.filter(a=>a?.config?.showInList)?.length||0," cột • STT & Số Hồ Sơ tự động"]})]}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:["Tổng: ",(0,d.jsx)("span",{className:"font-semibold text-gray-900",children:a?.length||0})," vụ việc"]})})]})}),u.length>0&&(0,d.jsx)("div",{className:"bg-blue-50 border-b border-blue-200 px-6 py-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsxs)("span",{className:"text-sm text-blue-700 font-medium",children:["Đ\xe3 chọn ",u.length," vụ việc"]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>{if(0===u.length)return void g.oR.warning("Vui l\xf2ng chọn \xedt nhất một vụ việc để x\xf3a");confirm(`Bạn c\xf3 chắc chắn muốn x\xf3a ${u.length} vụ việc đ\xe3 chọn?`)&&(h(u,"delete"),w([]),y(!1))},className:"flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium",children:"X\xf3a đ\xe3 chọn"}),(0,d.jsx)("button",{onClick:()=>{w([]),y(!1)},className:"flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors",children:"Bỏ chọn"})]})]})}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[t("court_case_delete")&&(0,d.jsx)("th",{className:"px-4 py-4 text-left w-12",children:(0,d.jsx)("input",{type:"checkbox",checked:x,onChange:b=>(b=>{y(b),b?w(a.map(a=>a._id)):w([])})(b.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-16",children:i?(0,d.jsx)(I,{field:"createdAt",children:"STT"}):"STT"}),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:i?(0,d.jsx)(I,{field:"soHoSo",children:"SỐ HỒ SƠ"}):"SỐ HỒ SƠ"}),B.filter(a=>a.config.showInList).map(a=>(0,d.jsx)("th",{className:`px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider ${a.width||"w-32"}`,style:{width:a.config.columnWidth||150},children:i?(0,d.jsx)(I,{field:a.name,children:a.label}):a.label},a._id)),(0,d.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"THAO T\xc1C"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map((a,e)=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors cursor-pointer",onClick:()=>b(a),children:[t("court_case_delete")&&(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,d.jsx)("input",{type:"checkbox",checked:u.includes(a._id),onChange:b=>{b.stopPropagation(),((a,b)=>{b?w(b=>[...b,a]):(w(b=>b.filter(b=>b!==a)),y(!1))})(a._id,b.target.checked)},className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,d.jsx)("span",{className:"font-semibold",children:a.stt||e+1})}),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,d.jsx)("span",{className:"font-mono",children:a.soHoSo||"Chưa c\xf3"})}),B.filter(a=>a.config.showInList).map(b=>(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",style:{maxWidth:b.config.columnWidth||150},children:"select"===b.dataType?(0,d.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${(a=>{switch(a){case"Chưa giải quyết":return"bg-red-100 text-red-800 border-red-200";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(J(a,b))}`,children:J(a,b)}):(a=>"date"===a.dataType||"datetime"===a.dataType)(b)?(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("div",{className:"truncate",title:J(a,b),children:J(a,b)}),(()=>{let c,e=(c=b.name,F.find(a=>a.fieldName===c&&a.countdownConfig.enabled));if(e&&e.countdownConfig.showCountdownBadge){let c=J(a,b);if(c&&(a=>{if(!a||""===a.trim())return!1;if(a.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[b,c,d]=a.split("/"),e=new Date(parseInt(d),parseInt(c)-1,parseInt(b));return!isNaN(e.getTime())&&e.getDate()===parseInt(b)&&e.getMonth()===parseInt(c)-1&&e.getFullYear()===parseInt(d)}return!isNaN(new Date(a).getTime())})(c))return(0,d.jsx)(v,{date:c,warningDays:e.countdownConfig.warningDays,dangerDays:e.countdownConfig.dangerDays,size:"sm",showIcon:!0,showText:!0})}return null})()]}):(0,d.jsx)("div",{className:"truncate",title:J(a,b),children:J(a,b)})},b._id)),(0,d.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("button",{onClick:c=>{c.stopPropagation(),b(a)},className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,d.jsx)(j.A,{size:16})}),t("court_case_edit")&&(0,d.jsx)("button",{onClick:b=>{b.stopPropagation(),c(a)},className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,d.jsx)(k.A,{size:16})}),t("court_case_delete")&&(0,d.jsx)("button",{onClick:b=>{b.stopPropagation(),f(a._id)},className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,d.jsx)(l.A,{size:16})})]})})]},a._id))})]})}),0===a.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCB"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Kh\xf4ng c\xf3 vụ việc n\xe0o"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Chưa c\xf3 dữ liệu vụ việc t\xf2a \xe1n để hiển thị."})]})]})};var x=c(31980),y=c(55550),z=c(65923);let A=z.Ik({});A.partial(),z.Ik({page:z.ai().int().positive().default(1),limit:z.ai().int().positive().max(100).default(20),search:z.Yj().optional(),sortBy:z.Yj().default("createdAt"),sortOrder:z.k5(["asc","desc"]).default("desc")});let B=A.extend({_id:z.Yj(),stt:z.ai(),soHoSo:z.Yj(),createdAt:z.Yj(),updatedAt:z.Yj(),createdBy:z.Ik({_id:z.Yj(),username:z.Yj(),email:z.Yj().optional()}).optional(),updatedBy:z.Ik({_id:z.Yj(),username:z.Yj(),email:z.Yj().optional()}).optional(),customFields:z.g1(z.bz()).optional()});z.Ik({success:z.zM(),cases:z.YO(B),pagination:z.Ik({currentPage:z.ai(),totalPages:z.ai(),totalItems:z.ai(),itemsPerPage:z.ai(),hasNextPage:z.zM(),hasPrevPage:z.zM()})}),z.Ik({success:z.zM(),stats:z.Ik({total:z.ai()})});let C=({courtCase:a,onSubmit:b,onCancel:c,loading:f=!1})=>{let h=!!a,[i,j]=(0,e.useState)([]),[k,l]=(0,e.useState)({}),{register:n,handleSubmit:p,formState:{errors:q},reset:r,setValue:s,watch:t}=(0,x.mN)({resolver:(0,y.u)(A),defaultValues:{}});(0,e.useEffect)(()=>{u()},[]),(0,e.useEffect)(()=>{let a=a=>{"Escape"===a.key&&c()};return document.addEventListener("keydown",a),()=>{document.removeEventListener("keydown",a)}},[c]),(0,e.useEffect)(()=>{if(a&&(r({...a}),a.customFields)){let b={...a.customFields};i.forEach(a=>{"date"===a.dataType&&b[a.name]&&!b[a.name].match(/^\d{2}\/\d{2}\/\d{4}$/)&&(b[a.name]=(0,m.Yq)(b[a.name]))}),l(b)}},[a,r,i]);let u=async()=>{try{let a=localStorage.getItem("sessionToken")||"",b=await o.A.getCustomFields("CourtCase",a);b.payload.success&&j(b.payload.fields)}catch(a){console.error("Error fetching custom fields:",a)}},v=(a,b)=>{l(c=>({...c,[a]:b}))};return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4",onClick:a=>{a.target===a.currentTarget&&c()},children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden relative",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:h?"✏️":"➕"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:h?"Chỉnh sửa vụ việc":"Th\xeam vụ việc mới"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:h?"Cập nhật vụ việc (chỉ trường t\xf9y chỉnh)":`Tạo vụ việc mới với STT & Số Hồ Sơ tự động (${i.length} trường t\xf9y chỉnh)`})]})]}),(0,d.jsx)("button",{onClick:c,disabled:f,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50",children:"✕"})]}),(0,d.jsx)("div",{className:"overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]",children:(0,d.jsx)("form",{id:"court-case-form",onSubmit:p(a=>{let c=[];if(c.length>0)return void c.forEach(a=>g.oR.error(a));b({...a,customFields:k})}),className:"p-6 space-y-8",children:i.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2",children:"⚙️ Th\xf4ng tin bổ sung"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map(a=>(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{htmlFor:`custom_${a.name}`,className:"block text-sm font-medium text-gray-700 mb-2",children:[a.label,a.config.required&&(0,d.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(a=>{let b=k[a.name]||a.config.defaultValue||"",c=`custom_${a.name}`;switch(a.dataType){case"text":case"email":case"phone":case"url":return(0,d.jsx)("input",{id:c,type:"email"===a.dataType?"email":"phone"===a.dataType?"tel":"url"===a.dataType?"url":"text",value:b,onChange:b=>v(a.name,b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:a.description||`Nhập ${a.label.toLowerCase()}`,required:a.config.required,maxLength:a.config.maxLength,minLength:a.config.minLength,pattern:a.config.pattern});case"textarea":return(0,d.jsx)("textarea",{id:c,value:b,onChange:b=>v(a.name,b.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:a.description||`Nhập ${a.label.toLowerCase()}`,required:a.config.required,maxLength:a.config.maxLength,minLength:a.config.minLength});case"number":case"currency":case"percentage":return(0,d.jsx)("input",{id:c,type:"number",value:b,onChange:b=>v(a.name,parseFloat(b.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:a.description||`Nhập ${a.label.toLowerCase()}`,required:a.config.required,min:a.config.min,max:a.config.max,step:a.config.decimals?`0.${"0".repeat(a.config.decimals-1)}1`:"1"});case"date":return(0,d.jsx)("input",{id:c,type:"text",value:b,onChange:b=>v(a.name,b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"dd/mm/yyyy",required:a.config.required,pattern:"^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\\d{4}$",title:"Vui l\xf2ng nhập ng\xe0y theo định dạng dd/mm/yyyy"});case"datetime":return(0,d.jsx)("input",{id:c,type:"datetime-local",value:b?new Date(b).toISOString().slice(0,16):"",onChange:b=>v(a.name,b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:a.config.required});case"boolean":return(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"radio",name:c,checked:!0===b,onChange:()=>v(a.name,!0),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{children:"C\xf3"})]}),(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"radio",name:c,checked:!1===b,onChange:()=>v(a.name,!1),className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{children:"Kh\xf4ng"})]})]});case"select":return(0,d.jsxs)("select",{id:c,value:b,onChange:b=>v(a.name,b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:a.config.required,children:[(0,d.jsxs)("option",{value:"",children:["-- Chọn ",a.label.toLowerCase()," --"]}),a.config.options?.map(a=>(0,d.jsx)("option",{value:a.value,children:a.label},a.value))]});case"multiselect":return(0,d.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2",children:a.config.options?.map(c=>(0,d.jsxs)("label",{className:"flex items-center gap-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:Array.isArray(b)&&b.includes(c.value),onChange:d=>{let e=Array.isArray(b)?b:[];d.target.checked?v(a.name,[...e,c.value]):v(a.name,e.filter(a=>a!==c.value))},className:"text-blue-600 focus:ring-blue-500"}),(0,d.jsx)("span",{className:"text-sm",children:c.label})]},c.value))});default:return(0,d.jsx)("input",{id:c,type:"text",value:b,onChange:b=>v(a.name,b.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:a.description||`Nhập ${a.label.toLowerCase()}`,required:a.config.required})}})(a),a.description&&(0,d.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:a.description})]},a._id))})]})})}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200 bg-gray-50",children:[(0,d.jsx)("button",{type:"button",onClick:c,className:"px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,d.jsx)("button",{type:"submit",form:"court-case-form",disabled:f,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Đang xử l\xfd...":h?"Cập nhật":"Tạo mới"})]}),f&&(0,d.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10",children:(0,d.jsxs)("div",{className:"flex flex-col items-center gap-3",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:h?"Đang cập nhật...":"Đang tạo vụ việc..."})]})})]})})},D=({courtCase:a,onClose:b,onEdit:c,onDelete:f})=>{let{hasPermission:h}=(0,n.S)(),[i,j]=(0,e.useState)([]),[r,s]=(0,e.useState)([]),[t,u]=(0,e.useState)(!0);(0,e.useEffect)(()=>{w()},[]);let w=async()=>{try{u(!0);let a=localStorage.getItem("sessionToken")||"",[b,c,d]=await Promise.all([o.A.getCustomFields("CourtCase",a),p.A.getFieldConfiguration("CourtCase",a),q.A.getDateCountdowns("CourtCase",a)]);if(b.payload.success&&c.payload.success){let a=[];b.payload.fields.forEach(b=>{a.push({...b})}),a.sort((a,b)=>(a.config.sortOrder||0)-(b.config.sortOrder||0)),j(a)}d.payload.success&&s(d.payload.countdowns)}catch(a){console.error("Error fetching fields and countdowns:",a),g.oR.error("C\xf3 lỗi xảy ra khi tải th\xf4ng tin trường")}finally{u(!1)}};return(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsxs)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("span",{className:"text-3xl mr-3",children:"\uD83D\uDCCB"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"Chi tiết vụ việc"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Th\xf4ng tin chi tiết vụ việc t\xf2a \xe1n"})]})]}),(0,d.jsx)("button",{onClick:b,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),t?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-600",children:"Đang tải th\xf4ng tin..."})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",style:{color:"#111827"},children:"Th\xf4ng tin hệ thống"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:["STT",(0,d.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded",children:"Tự động"})]}),(0,d.jsx)("div",{className:"text-lg font-semibold",style:{color:"#111827"},children:a.stt})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:["Số Hồ Sơ",(0,d.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded",children:"XX/DD/MM/YYYY"})]}),(0,d.jsx)("div",{className:"text-lg font-mono font-semibold",style:{color:"#111827"},children:a.soHoSo||"Chưa c\xf3"})]})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDCCB Th\xf4ng tin chi tiết"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.filter(a=>!1!==a.config.showInDetail).map(b=>{let c,e=a.customFields?.[b.name]||"",f=a.customFields?.[b.name],g=(a=>"date"===a.dataType||"datetime"===a.dataType)(b)?(c=b.name,r.find(a=>a.fieldName===c&&a.countdownConfig.enabled&&a.countdownConfig.showCountdownBadge)):null;return(0,d.jsxs)("div",{className:"textarea"===b.dataType?"md:col-span-2":"",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:[b.label,(0,d.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-600 rounded",children:"T\xf9y chỉnh"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"flex-1",style:{color:"#111827"},children:((a,b)=>{if(!b&&0!==b)return(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 dữ liệu"});switch(a.dataType){case"select":if("trangThaiGiaiQuyet"===a.name)return(0,d.jsx)("span",{className:`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${(a=>{switch(a){case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800";case"Chưa giải quyết":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(b)}`,children:b});return(0,d.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:b});case"textarea":return(0,d.jsx)("div",{className:"whitespace-pre-wrap",children:b});case"number":return(0,d.jsx)("span",{className:"font-mono",children:b.toLocaleString()});case"date":return(0,d.jsx)("span",{children:(0,m.Yq)(b)});case"datetime":if(!b)return(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 dữ liệu"});let c=new Date(b);if(isNaN(c.getTime()))return(0,d.jsx)("span",{className:"text-gray-400 italic",children:"Ng\xe0y kh\xf4ng hợp lệ"});let e=c.getDate().toString().padStart(2,"0"),f=(c.getMonth()+1).toString().padStart(2,"0"),g=c.getFullYear().toString(),h=c.getHours().toString().padStart(2,"0"),i=c.getMinutes().toString().padStart(2,"0");return(0,d.jsx)("span",{children:`${e}/${f}/${g} ${h}:${i}`});case"boolean":return b?"✅ C\xf3":"❌ Kh\xf4ng";default:return(0,d.jsx)("span",{children:b})}})(b,e)}),g&&f&&(0,d.jsx)(v,{date:f,warningDays:g.countdownConfig.warningDays,dangerDays:g.countdownConfig.dangerDays,size:"sm",showIcon:!0,showText:!0})]})]},b._id)})})]}),(0,d.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"ℹ️ Th\xf4ng tin hệ thống"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.createdBy&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người tạo"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.createdBy.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y tạo"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,m.Yq)(a.createdAt)})]})]}),a.updatedBy&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người cập nhật cuối"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:a.updatedBy.username})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y cập nhật cuối"}),(0,d.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,m.Yq)(a.updatedAt)})]})]})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:[(0,d.jsx)("button",{onClick:b,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"}),h("court_case_edit")&&(0,d.jsxs)("button",{onClick:()=>{b(),c(a)},className:"flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#059669",color:"#ffffff"},children:[(0,d.jsx)(k.A,{size:16}),"Chỉnh sửa"]}),h("court_case_delete")&&(0,d.jsxs)("button",{onClick:()=>{confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?")&&(f(a._id),b())},className:"flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:[(0,d.jsx)(l.A,{size:16}),"X\xf3a"]})]})]})})};var E=c(70469),F=c(26691),G=c(44943);let H=(0,F.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),I=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},g)=>{let h=e?E.DX:"button";return(0,d.jsx)(h,{className:(0,G.cn)(H({variant:b,size:c,className:a})),ref:g,...f})});I.displayName="Button";var J=c(31210),K=c(80755),L=c(47089),M=c(43700);let N=({preview:a,onConfirmImport:b,onCancel:c,isImporting:f})=>{let[g,h]=(0,e.useState)(!1),[i,k]=(0,e.useState)("all"),l="all"===i?a.data:a.data.filter(a=>a.status===i);return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Xem trước dữ liệu import"}),(0,d.jsxs)(I,{variant:"outline",onClick:c,className:"text-gray-600 hover:text-gray-800",children:[(0,d.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"Đ\xf3ng"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)(J.Zp,{children:[(0,d.jsx)(J.aR,{className:"pb-2",children:(0,d.jsx)(J.ZB,{className:"text-sm font-medium text-gray-600",children:"Tổng số d\xf2ng"})}),(0,d.jsx)(J.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:a.summary.totalRows})})]}),(0,d.jsxs)(J.Zp,{children:[(0,d.jsx)(J.aR,{className:"pb-2",children:(0,d.jsx)(J.ZB,{className:"text-sm font-medium text-gray-600",children:"Hợp lệ"})}),(0,d.jsx)(J.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:a.summary.validRows})})]}),(0,d.jsxs)(J.Zp,{children:[(0,d.jsx)(J.aR,{className:"pb-2",children:(0,d.jsx)(J.ZB,{className:"text-sm font-medium text-gray-600",children:"Cảnh b\xe1o"})}),(0,d.jsx)(J.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:a.summary.warningRows})})]}),(0,d.jsxs)(J.Zp,{children:[(0,d.jsx)(J.aR,{className:"pb-2",children:(0,d.jsx)(J.ZB,{className:"text-sm font-medium text-gray-600",children:"Lỗi"})}),(0,d.jsx)(J.Wu,{children:(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:a.summary.errorRows})})]})]}),a.warnings.length>0&&(0,d.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(s.A,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-yellow-800 mb-2",children:"Cảnh b\xe1o:"}),(0,d.jsx)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:a.warnings.map((a,b)=>(0,d.jsx)("li",{className:"text-sm",children:a},b))})]})]})}),a.errors.length>0&&(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)(r.A,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-red-800 mb-2",children:"Lỗi:"}),(0,d.jsx)("ul",{className:"list-disc list-inside space-y-1 text-red-700",children:a.errors.map((a,b)=>(0,d.jsx)("li",{className:"text-sm",children:a},b))})]})]})}),(0,d.jsx)(J.Zp,{children:(0,d.jsx)(J.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{children:a.summary.canImport?(0,d.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,d.jsx)(u.A,{className:"h-5 w-5 mr-2"}),(0,d.jsx)("span",{className:"font-medium",children:"C\xf3 thể import được"})]}):(0,d.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,d.jsx)(r.A,{className:"h-5 w-5 mr-2"}),(0,d.jsx)("span",{className:"font-medium",children:"Kh\xf4ng thể import do c\xf3 lỗi"})]})}),(0,d.jsxs)("div",{className:"space-x-2",children:[(0,d.jsxs)(I,{variant:"outline",onClick:()=>h(!g),children:[(0,d.jsx)(j.A,{className:"h-4 w-4 mr-2"}),g?"Ẩn chi tiết":"Xem chi tiết"]}),(0,d.jsxs)(I,{onClick:b,disabled:!a.summary.canImport||f,className:"bg-blue-600 hover:bg-blue-700",children:[(0,d.jsx)(M.A,{className:"h-4 w-4 mr-2"}),f?"Đang import...":"X\xe1c nhận import"]})]})]})})}),g&&(0,d.jsxs)(J.Zp,{children:[(0,d.jsx)(J.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(J.ZB,{children:"Chi tiết dữ liệu"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)(I,{variant:"all"===i?"default":"outline",size:"sm",onClick:()=>k("all"),children:["Tất cả (",a.summary.totalRows,")"]}),(0,d.jsxs)(I,{variant:"valid"===i?"default":"outline",size:"sm",onClick:()=>k("valid"),children:["Hợp lệ (",a.summary.validRows,")"]}),(0,d.jsxs)(I,{variant:"warning"===i?"default":"outline",size:"sm",onClick:()=>k("warning"),children:["Cảnh b\xe1o (",a.summary.warningRows,")"]}),(0,d.jsxs)(I,{variant:"error"===i?"default":"outline",size:"sm",onClick:()=>k("error"),children:["Lỗi (",a.summary.errorRows,")"]})]})]})}),(0,d.jsx)(J.Wu,{children:(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"bg-gray-50",children:[(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"D\xf2ng"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Trạng th\xe1i"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Số thụ l\xfd"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Loại \xe1n"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Ng\xe0y thụ l\xfd"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Bị c\xe1o/NĐ/NKK"}),(0,d.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Lỗi/Cảnh b\xe1o"})]})}),(0,d.jsx)("tbody",{children:l.map((a,b)=>(0,d.jsxs)("tr",{className:`
                      ${"error"===a.status?"bg-red-50":"warning"===a.status?"bg-yellow-50":"bg-green-50"}
                    `,children:[(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.rowNumber}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(a=>{switch(a){case"valid":return(0,d.jsx)(u.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,d.jsx)(s.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,d.jsx)(r.A,{className:"h-4 w-4 text-red-500"});default:return null}})(a.status),(0,d.jsx)("span",{className:"ml-2",children:(a=>{switch(a){case"valid":return(0,d.jsx)(K.Ex,{variant:"success",children:"Hợp lệ"});case"warning":return(0,d.jsx)(K.Ex,{variant:"warning",children:"Cảnh b\xe1o"});case"error":return(0,d.jsx)(K.Ex,{variant:"danger",children:"Lỗi"});default:return null}})(a.status)})]})}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.soThuLy}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.loaiAn}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.ngayThuLyDisplay||""}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2 max-w-xs truncate",children:a.biCaoNguoiKhieuKien}),(0,d.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:a.validationErrors.length>0&&(0,d.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:a.validationErrors.map((a,b)=>(0,d.jsxs)("li",{children:["• ",a]},b))})})]},b))})]})})})]})]})},O=({onImportComplete:a,onClose:b})=>{let[c,f]=(0,e.useState)(null),[h,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(null),[o,p]=(0,e.useState)(null),q=(0,e.useRef)(null),r=async()=>{if(!c)return void g.oR.error("Vui l\xf2ng chọn file Excel");try{l(!0);let a=await i.previewImport(c);a.payload.success?n(a.payload.preview):g.oR.error("Kh\xf4ng thể xem trước file")}catch(a){console.error("Error previewing:",a),g.oR.error("C\xf3 lỗi xảy ra khi xem trước file")}finally{l(!1)}},s=async()=>{if(!c)return void g.oR.error("Vui l\xf2ng chọn file Excel");try{j(!0);let b=await i.importCourtCases(c);b.payload.success?(p(b.payload.results),n(null),g.oR.success(`Import th\xe0nh c\xf4ng! ${b.payload.results.success}/${b.payload.results.total} vụ việc`),a()):g.oR.error("Import thất bại")}catch(a){console.error("Error importing:",a),g.oR.error("C\xf3 lỗi xảy ra khi import file")}finally{j(!1)}},t=async()=>{try{await i.downloadTemplate(),g.oR.success("Đ\xe3 tải xuống file mẫu")}catch(a){console.error("Error downloading template:",a),g.oR.error("Kh\xf4ng thể tải xuống file mẫu")}};return(0,d.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,d.jsx)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:m?(0,d.jsx)(N,{preview:m,onConfirmImport:s,onCancel:()=>n(null),isImporting:h}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCE5 Import vụ việc từ Excel"}),(0,d.jsx)("button",{onClick:b,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Hướng dẫn:"}),(0,d.jsxs)("ul",{className:"text-sm space-y-1",style:{color:"#111827"},children:[(0,d.jsx)("li",{children:"• File Excel phải c\xf3 định dạng .xlsx hoặc .xls"}),(0,d.jsx)("li",{children:"• D\xf2ng đầu ti\xean l\xe0 ti\xeau đề cột (sẽ bị bỏ qua)"}),(0,d.jsx)("li",{children:"• STT c\xf3 thể để trống (hệ thống tự tạo)"}),(0,d.jsx)("li",{children:"• Ng\xe0y th\xe1ng theo định dạng YYYY-MM-DD hoặc DD/MM/YYYY"}),(0,d.jsx)("li",{children:"• C\xe1c trường kh\xe1c c\xf3 thể để trống"}),(0,d.jsxs)("li",{children:["• ",(0,d.jsx)("strong",{children:"Khuyến kh\xedch xem trước dữ liệu trước khi import"})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center p-4 border border-gray-200 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCC4 File mẫu"}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tham khảo định dạng"})]}),(0,d.jsx)("button",{onClick:t,className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Tải file mẫu"})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Chọn file Excel"}),(0,d.jsx)("input",{ref:q,type:"file",accept:".xlsx,.xls",onChange:a=>{let b=a.target.files?.[0];if(b){if(!b.name.endsWith(".xlsx")&&!b.name.endsWith(".xls"))return void g.oR.error("Vui l\xf2ng chọn file Excel (.xlsx hoặc .xls)");f(b),p(null),n(null)}},className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),c&&(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("p",{className:"text-sm",style:{color:"#111827"},children:[(0,d.jsx)("strong",{children:"File đ\xe3 chọn:"})," ",c.name]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["K\xedch thước: ",(c.size/1024).toFixed(2)," KB"]})]})]}),o&&(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCCA Kết quả import:"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:o.total}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng số d\xf2ng"})]}),(0,d.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.success}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Th\xe0nh c\xf4ng"})]}),(0,d.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:o.duplicates}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Tr\xf9ng lặp"})]}),(0,d.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-red-600",children:o.errors.length}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Lỗi"})]})]}),o.errors.length>0&&(0,d.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,d.jsx)("h5",{className:"font-medium text-red-800 mb-2",children:"❌ Lỗi chi tiết:"}),(0,d.jsx)("div",{className:"max-h-32 overflow-y-auto",children:o.errors.map((a,b)=>(0,d.jsxs)("p",{className:"text-sm text-red-700",children:["• ",a]},b))})]})]}),(0,d.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,d.jsx)("button",{onClick:b,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},disabled:h||k,children:"Đ\xf3ng"}),(0,d.jsx)("button",{onClick:r,disabled:!c||h||k,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#059669",color:"#ffffff"},children:k?"Đang xem trước...":"\uD83D\uDC41️ Xem trước"}),(0,d.jsx)("button",{onClick:s,disabled:!c||h||k,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:h?"Đang import...":"\uD83D\uDCE5 Import trực tiếp"})]})]})]})})})},P=({searchParams:a,onSearch:b,onReset:c})=>{let g=`
    .date-input::-webkit-calendar-picker-indicator {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3e%3cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd'/%3e%3c/svg%3e");
      cursor: pointer;
    }
    .date-input::-webkit-datetime-edit-text {
      color: transparent;
    }
    .date-input::-webkit-datetime-edit-month-field {
      color: transparent;
    }
    .date-input::-webkit-datetime-edit-day-field {
      color: transparent;
    }
    .date-input::-webkit-datetime-edit-year-field {
      color: transparent;
    }
    .date-input:focus::-webkit-datetime-edit-text,
    .date-input:focus::-webkit-datetime-edit-month-field,
    .date-input:focus::-webkit-datetime-edit-day-field,
    .date-input:focus::-webkit-datetime-edit-year-field {
      color: inherit;
    }
    .date-input[value]::-webkit-datetime-edit-text,
    .date-input[value]::-webkit-datetime-edit-month-field,
    .date-input[value]::-webkit-datetime-edit-day-field,
    .date-input[value]::-webkit-datetime-edit-year-field {
      color: inherit;
    }
  `;f().useEffect(()=>{let a=document.createElement("style");return a.textContent=g,document.head.appendChild(a),()=>document.head.removeChild(a)},[]);let[h,i]=(0,e.useState)(!1),[j,k]=(0,e.useState)([]);(0,e.useEffect)(()=>{(async()=>{try{let a=localStorage.getItem("sessionToken")||"",b=await o.A.getCustomFields("CourtCase",a);b.payload.success&&k(b.payload.fields)}catch(a){console.error("Error fetching custom fields:",a)}})()},[]);let l=[{key:"soHoSo",label:"Số Hồ Sơ",type:"text",placeholder:"Nhập số hồ sơ (VD: 01/28/08/2025)"}],m=[...l];j.forEach(a=>{"date"===a.dataType?m.push({key:a.name,label:a.label,type:"daterange",placeholder:`Chọn khoảng thời gian`}):m.push({key:a.name,label:a.label,type:"text"===a.dataType?"text":"number"===a.dataType?"number":"select"===a.dataType?"select":"text",options:"select"===a.dataType&&a.config?.options?["",...a.config.options]:void 0,placeholder:`Nhập ${a.label.toLowerCase()}`})});let n=m.filter(b=>{if("daterange"===b.type){let c=a[`${b.key}_from`],d=a[`${b.key}_to`];return c&&""!==c||d&&""!==d}{let c=a[b.key];return c&&""!==c&&void 0!==c}}).length,p=(a,c)=>{b({[a]:c||void 0})},q=a=>{if(!a)return"";if(a.match(/^\d{4}-\d{2}-\d{2}$/))return a;if(a.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[b,c,d]=a.split("/");return`${d}-${c}-${b}`}return a},r=(a,b,c)=>{let d=`${a}_from`,e=`${a}_to`,f=(a=>{if(!a)return"";if(a.match(/^\d{4}-\d{2}-\d{2}$/)){let[b,c,d]=a.split("-");return`${d}/${c}/${b}`}return a})(c);"from"===b?(p(d,f),setTimeout(()=>{let a=document.querySelector(`input[data-field="${e}"]`);a&&c&&a.focus()},100)):p(e,f)};return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>i(!h),children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"text-xl",children:"\uD83D\uDD0D"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Bộ lọc theo trường t\xf9y chỉnh"}),n>0&&(0,d.jsxs)("p",{className:"text-sm text-blue-600",children:[n," bộ lọc đang được \xe1p dụng"]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[!h&&n>0&&(0,d.jsx)("button",{onClick:a=>{a.stopPropagation(),c()},className:"px-3 py-1 text-xs bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors",children:"X\xf3a tất cả"}),(0,d.jsx)("div",{className:`transform transition-transform duration-200 ${h?"rotate-180":""}`,children:(0,d.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,d.jsx)("div",{className:`transition-all duration-300 ease-in-out ${h?"max-h-none opacity-100":"max-h-0 opacity-0 overflow-hidden"}`,children:(0,d.jsxs)("div",{className:"p-4 border-t border-gray-100",children:[l.length>0&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-4 flex items-center gap-2",children:[(0,d.jsx)("span",{children:"\uD83C\uDFDB️"}),"Bộ lọc hệ thống"]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:l.map(b=>(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-2",children:b.label}),(b=>{let c=a[b.key]||"";return(0,d.jsx)("input",{type:"text",value:c,onChange:a=>p(b.key,a.target.value),placeholder:b.placeholder,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})})(b)]},b.key))})]}),j.length>0?(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-4 flex items-center gap-2",children:[(0,d.jsx)("span",{children:"⚙️"}),"Bộ lọc theo trường t\xf9y chỉnh"]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.slice(l.length).map(b=>(0,d.jsxs)("div",{className:"daterange"===b.type?"md:col-span-2":"",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1",children:["daterange"===b.type&&(0,d.jsx)("span",{children:"\uD83D\uDCC5"}),"select"===b.type&&(0,d.jsx)("span",{children:"\uD83D\uDCCB"}),"number"===b.type&&(0,d.jsx)("span",{children:"\uD83D\uDD22"}),"text"===b.type&&(0,d.jsx)("span",{children:"\uD83D\uDCDD"}),b.label,"daterange"===b.type&&(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"(Khoảng thời gian)"})]}),(b=>{switch(b.type){case"daterange":let c=a[`${b.key}_from`]||"",e=a[`${b.key}_to`]||"";return(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)("input",{type:"date",value:q(c),onChange:a=>r(b.key,"from",a.target.value),onFocus:a=>{let b=a.target.parentElement?.querySelector(".custom-placeholder");b&&(b.style.display="none")},onBlur:a=>{let b=a.target.parentElement?.querySelector(".custom-placeholder");b&&!a.target.value&&(b.style.display="block")},"data-field":`${b.key}_from`,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent date-input",style:{colorScheme:"light"}}),!c&&(0,d.jsx)("div",{className:"custom-placeholder absolute left-2 top-2 text-gray-400 text-sm pointer-events-none",children:"dd/mm/yyyy"})]}),(0,d.jsx)("span",{className:"text-gray-400 text-sm",children:"→"}),(0,d.jsxs)("div",{className:"flex-1 relative",children:[(0,d.jsx)("input",{type:"date",value:q(e),onChange:a=>r(b.key,"to",a.target.value),onFocus:a=>{let b=a.target.parentElement?.querySelector(".custom-placeholder");b&&(b.style.display="none")},onBlur:a=>{let b=a.target.parentElement?.querySelector(".custom-placeholder");b&&!a.target.value&&(b.style.display="block")},"data-field":`${b.key}_to`,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent date-input",style:{colorScheme:"light"}}),!e&&(0,d.jsx)("div",{className:"custom-placeholder absolute left-2 top-2 text-gray-400 text-sm pointer-events-none",children:"dd/mm/yyyy"})]})]});case"select":let f=a[b.key]||"";return(0,d.jsx)("select",{value:f,onChange:a=>p(b.key,a.target.value),className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",children:b.options?.map((a,b)=>(0,d.jsx)("option",{value:a,children:a||"Tất cả"},b))});case"number":let g=a[b.key]||"";return(0,d.jsx)("input",{type:"number",value:g,onChange:a=>p(b.key,a.target.value),placeholder:b.placeholder,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"});default:let h=a[b.key]||"";return(0,d.jsx)("input",{type:"text",value:h,onChange:a=>p(b.key,a.target.value),placeholder:b.placeholder,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"})}})(b)]},b.key))})]}):(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDD"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Chưa c\xf3 trường t\xf9y chỉnh"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Tạo c\xe1c trường t\xf9y chỉnh để c\xf3 thể lọc dữ liệu theo nhu cầu"}),(0,d.jsxs)("a",{href:"/dashboard/court-cases/custom-fields",className:"inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("span",{children:"➕"}),"Tạo trường t\xf9y chỉnh"]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:n>0?(0,d.jsxs)("span",{className:"text-blue-600 font-medium",children:[n," bộ lọc đang hoạt động"]}):"Chưa c\xf3 bộ lọc n\xe0o được \xe1p dụng"}),(0,d.jsxs)("button",{onClick:c,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2",children:[(0,d.jsx)("span",{children:"\uD83D\uDDD1️"}),"X\xf3a tất cả bộ lọc"]})]})]})})]})};var Q=c(74701);let R=()=>{let{hasPermission:a}=(0,n.S)(),[b,c]=(0,e.useState)([]),[f,h]=(0,e.useState)(!1),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(null),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"}),[u,v]=(0,e.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:20,hasNextPage:!1,hasPrevPage:!1}),x=async()=>{try{h(!0);let a=await i.getCourtCases(s);a.payload.success?(c(a.payload.cases),v(a.payload.pagination)):g.oR.error("Kh\xf4ng thể tải danh s\xe1ch vụ việc")}catch(a){console.error("Error fetching court cases:",a),g.oR.error("C\xf3 lỗi xảy ra khi tải danh s\xe1ch vụ việc")}finally{h(!1)}};(0,e.useEffect)(()=>{x()},[s]);let y=a=>{t(b=>({...b,page:a}))},z=async a=>{try{h(!0),(await i.createCourtCase(a)).payload.success?(g.oR.success("Th\xeam vụ việc th\xe0nh c\xf4ng"),p(!1),x()):g.oR.error("Kh\xf4ng thể th\xeam vụ việc")}catch(a){console.error("Error creating court case:",a),g.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi th\xeam vụ việc")}finally{h(!1)}},A=async a=>{if(l)try{h(!0),(await i.updateCourtCase(l._id,a)).payload.success?(g.oR.success("Cập nhật vụ việc th\xe0nh c\xf4ng"),m(null),x()):g.oR.error("Kh\xf4ng thể cập nhật vụ việc")}catch(a){console.error("Error updating court case:",a),g.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi cập nhật vụ việc")}finally{h(!1)}},B=async a=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?"))try{(await i.deleteCourtCase(a)).payload.success?(g.oR.success("X\xf3a vụ việc th\xe0nh c\xf4ng"),x()):g.oR.error("Kh\xf4ng thể x\xf3a vụ việc")}catch(a){console.error("Error deleting court case:",a),g.oR.error(a.payload?.message||"C\xf3 lỗi xảy ra khi x\xf3a vụ việc")}},E=async(a,b)=>{if("delete"===b)try{await i.bulkDeleteCourtCases(a),g.oR.success(`Đ\xe3 x\xf3a ${a.length} vụ việc`),x()}catch(a){console.error("Error bulk deleting:",a),g.oR.error("C\xf3 lỗi xảy ra khi x\xf3a h\xe0ng loạt")}},F=async()=>{try{g.oR.info("Đang xuất file Excel...");let a=await i.exportCourtCases(s),b=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),c=window.URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=`danh-sach-vu-viec-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(d),d.click(),document.body.removeChild(d),window.URL.revokeObjectURL(c),g.oR.success("Xuất file Excel th\xe0nh c\xf4ng")}catch(a){console.error("Error exporting Excel:",a),g.oR.error("C\xf3 lỗi xảy ra khi xuất file Excel")}};return(0,d.jsx)(Q.default,{requiredPermissions:["court_case_view"],children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Quản l\xfd vụ việc t\xf2a \xe1n"}),(0,d.jsx)("p",{className:"text-gray-600 mt-1",children:"Quản l\xfd danh s\xe1ch thụ l\xfd v\xe0 giải quyết vụ việc đề nghị gi\xe1m đốc thẩm, t\xe1i thẩm"})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[a("custom_fields_view")&&(0,d.jsx)("button",{onClick:()=>window.open("/dashboard/court-cases/custom-fields","_blank"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"⚙️ Quản l\xfd trường"}),a("court_case_import")&&(0,d.jsx)("button",{onClick:()=>r(!0),className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500",children:"\uD83D\uDCE5 Import Excel"}),a("court_case_export")&&(0,d.jsx)("button",{onClick:F,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500",children:"\uD83D\uDCCA Xuất Excel"}),a("court_case_create")&&(0,d.jsx)("button",{onClick:()=>p(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"➕ Th\xeam vụ việc mới"})]})]}),(0,d.jsx)(P,{searchParams:s,onSearch:a=>{t(b=>({...b,...a,page:1}))},onReset:()=>{t({page:1,limit:20,sortBy:"createdAt",sortOrder:"desc"})}}),(0,d.jsx)(w,{cases:b,onCaseSelect:k,onCaseEdit:m,onCaseDelete:B,onBulkAction:E,onSort:(a,b)=>{t(c=>({...c,sortBy:a,sortOrder:b,page:1}))},currentSort:{sortBy:s.sortBy,sortOrder:s.sortOrder},loading:f}),u.totalPages>1&&(0,d.jsx)("div",{className:"bg-white px-6 py-3 rounded-lg shadow",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"text-sm text-gray-700",children:["Hiển thị ",(u.currentPage-1)*u.itemsPerPage+1," đến"," ",Math.min(u.currentPage*u.itemsPerPage,u.totalItems)," trong tổng số"," ",u.totalItems," vụ việc"]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("button",{onClick:()=>y(u.currentPage-1),disabled:!u.hasPrevPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Trước"}),(0,d.jsxs)("span",{className:"px-3 py-2 text-sm text-gray-700",children:["Trang ",u.currentPage," / ",u.totalPages]}),(0,d.jsx)("button",{onClick:()=>y(u.currentPage+1),disabled:!u.hasNextPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Sau"})]})]})}),o&&(0,d.jsx)(C,{onSubmit:z,onCancel:()=>p(!1),loading:f}),l&&(0,d.jsx)(C,{courtCase:l,onSubmit:A,onCancel:()=>m(null),loading:f}),j&&(0,d.jsx)(D,{courtCase:j,onClose:()=>k(null),onEdit:m,onDelete:B}),q&&(0,d.jsx)(O,{onImportComplete:()=>{x()},onClose:()=>r(!1)})]})})}},72286:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getCustomFields:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),createCustomField:(a,b)=>d.Ay.post("/api/custom-fields",a,{headers:{Authorization:`Bearer ${b}`}}),updateCustomField:(a,b,c)=>d.Ay.put(`/api/custom-fields/${a}`,b,{headers:{Authorization:`Bearer ${c}`}}),deleteCustomField:(a,b)=>d.Ay.delete(`/api/custom-fields/${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldsOrder:(a,b)=>d.Ay.put("/api/custom-fields/order",{fieldOrders:a},{headers:{Authorization:`Bearer ${b}`}}),getCustomFieldStats:(a="CourtCase",b)=>d.Ay.get(`/api/custom-fields/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),getFieldsInDatabase:(a="CourtCase")=>d.Ay.get(`/api/custom-fields/database-fields?targetModel=${a}`)}},74701:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(21124),e=c(25816),f=c(42378);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isDepartmentManager:l,isLoading:m}=(0,e.S)(),n=(0,f.useRouter)();if(m)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?"admin"===b&&!!l||i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>n.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(38301)},75219:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},76180:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},77922:(a,b,c)=>{"use strict";c.d(b,{Yq:()=>e,ZV:()=>f,z3:()=>d});let d=(a,b=2)=>{if(0===a)return"0 Bytes";let c=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,c)).toFixed(b<0?0:b))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][c]},e=a=>{let b;if(!a)return"";if("string"==typeof a){if(a.match(/^\d{2}\/\d{2}\/\d{4}$/))return a;if(a.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[c,d,e]=a.split("/");b=new Date(parseInt(e),parseInt(d)-1,parseInt(c))}else b=new Date(a)}else b=new Date(a);if(isNaN(b.getTime()))return"";let c=b.getDate().toString().padStart(2,"0"),d=(b.getMonth()+1).toString().padStart(2,"0"),e=b.getFullYear().toString();return`${c}/${d}/${e}`},f=a=>a.toLocaleString()},79428:a=>{"use strict";a.exports=require("buffer")},80517:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getFieldConfiguration:(a="CourtCase",b)=>d.Ay.get(`/api/field-configurations?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),updateFieldConfiguration:(a,b)=>d.Ay.put("/api/field-configurations",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldOrder:(a,b)=>d.Ay.put("/api/field-configurations/order",a,{headers:{Authorization:`Bearer ${b}`}}),updateFieldVisibility:(a,b)=>d.Ay.put("/api/field-configurations/visibility",a,{headers:{Authorization:`Bearer ${b}`}})}},80755:(a,b,c)=>{"use strict";c.d(b,{Ex:()=>e,eG:()=>f});var d=c(21124);c(38301);let e=({children:a,variant:b="default",size:c="md",className:e="",dot:f=!1})=>(0,d.jsxs)("span",{className:`
        inline-flex items-center font-medium rounded-full
        ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[b]}
        ${{sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[c]}
        ${e}
      `,children:[f&&(0,d.jsx)("span",{className:`w-2 h-2 rounded-full mr-2 ${{default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[b]}`}),a]}),f=({role:a,className:b=""})=>{let c={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},f=c[a]||c.user;return(0,d.jsx)(e,{variant:f.variant,className:b,children:f.label})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90828:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={getDateCountdowns:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),upsertDateCountdown:(a,b)=>d.Ay.post("/api/date-countdowns",a,{headers:{Authorization:`Bearer ${b}`}}),deleteDateCountdown:(a,b)=>d.Ay.delete(`/api/date-countdowns/${a}`,{headers:{Authorization:`Bearer ${b}`}}),getCountdownStats:(a="CourtCase",b)=>d.Ay.get(`/api/date-countdowns/stats?targetModel=${a}`,{headers:{Authorization:`Bearer ${b}`}}),getUpcomingDeadlines:(a="CourtCase",b=30,c)=>d.Ay.get(`/api/date-countdowns/upcoming?targetModel=${a}&days=${b}`,{headers:{Authorization:`Bearer ${c}`}})}},91292:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(23339).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,4479,6975,1058,3445,3271],()=>b(b.s=34340));module.exports=c})();