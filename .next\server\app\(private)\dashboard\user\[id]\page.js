(()=>{var a={};a.id=3664,a.ids=[3664],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1354:(a,b,c)=>{Promise.resolve().then(c.bind(c,73520))},1590:(a,b,c)=>{"use strict";c.d(b,{lV:()=>l,MJ:()=>s,zB:()=>n,eI:()=>q,lR:()=>r,C5:()=>t});var d=c(21124),e=c(38301),f=c(70469),g=c(31980),h=c(44943),i=c(71498);let j=(0,c(26691).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(i.b,{ref:c,className:(0,h.cn)(j(),a),...b}));k.displayName=i.b.displayName;let l=g.Op,m=e.createContext({}),n=({...a})=>(0,d.jsx)(m.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),o=()=>{let a=e.useContext(m),b=e.useContext(p),{getFieldState:c,formState:d}=(0,g.xW)(),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},p=e.createContext({}),q=e.forwardRef(({className:a,...b},c)=>{let f=e.useId();return(0,d.jsx)(p.Provider,{value:{id:f},children:(0,d.jsx)("div",{ref:c,className:(0,h.cn)("mb-4",a),...b})})});q.displayName="FormItem";let r=e.forwardRef(({className:a,...b},c)=>{let{error:e,formItemId:f}=o();return(0,d.jsx)(k,{ref:c,className:(0,h.cn)(e&&"text-destructive",a),htmlFor:f,...b})});r.displayName="FormLabel";let s=e.forwardRef(({...a},b)=>{let{error:c,formItemId:e,formDescriptionId:g,formMessageId:h}=o();return(0,d.jsx)(f.DX,{ref:b,id:e,"aria-describedby":c?`${g} ${h}`:`${g}`,"aria-invalid":!!c,...a})});s.displayName="FormControl",e.forwardRef(({className:a,...b},c)=>{let{formDescriptionId:e}=o();return(0,d.jsx)("p",{ref:c,id:e,className:(0,h.cn)("text-[0.8rem] text-muted-foreground",a),...b})}).displayName="FormDescription";let t=e.forwardRef(({className:a,children:b,...c},e)=>{let{error:f,formMessageId:g}=o(),i=f?String(f?.message):b;return i?(0,d.jsx)("p",{ref:e,id:g,className:(0,h.cn)("text-[0.8rem] font-medium text-red-600",a),...c,children:i}):null});t.displayName="FormMessage"},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30756:(a,b,c)=>{"use strict";c.d(b,{he:()=>f,v_:()=>d});let d=[{id:"user_view",name:"Xem danh s\xe1ch người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin người d\xf9ng"},{id:"user_add",name:"Th\xeam người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể tạo t\xe0i khoản người d\xf9ng mới"},{id:"user_edit",name:"Chỉnh sửa người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể chỉnh sửa th\xf4ng tin người d\xf9ng"},{id:"user_delete",name:"X\xf3a người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể x\xf3a t\xe0i khoản người d\xf9ng"},{id:"user_import",name:"Import người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể import danh s\xe1ch người d\xf9ng từ file"},{id:"file_view",name:"Xem file",category:"Quản l\xfd file",description:"C\xf3 thể xem danh s\xe1ch file"},{id:"file_upload",name:"Upload file",category:"Quản l\xfd file",description:"C\xf3 thể upload file l\xean hệ thống"},{id:"file_delete",name:"X\xf3a file",category:"Quản l\xfd file",description:"C\xf3 thể x\xf3a file khỏi hệ thống"},{id:"file_download",name:"Tải file",category:"Quản l\xfd file",description:"C\xf3 thể tải file về m\xe1y"},{id:"court_case_view",name:"Xem vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 chi tiết vụ \xe1n"},{id:"court_case_create",name:"Tạo vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể tạo vụ \xe1n mới"},{id:"court_case_edit",name:"Chỉnh sửa vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể chỉnh sửa th\xf4ng tin vụ \xe1n"},{id:"court_case_delete",name:"X\xf3a vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể x\xf3a vụ \xe1n"},{id:"court_case_export",name:"Xuất dữ liệu vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xuất dữ liệu vụ \xe1n ra file"},{id:"court_case_import",name:"Import vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể import dữ liệu vụ \xe1n từ file"},{id:"court_case_stats",name:"Thống k\xea vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem thống k\xea v\xe0 b\xe1o c\xe1o về vụ \xe1n"},{id:"custom_fields_view",name:"Xem trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 cấu h\xecnh trường t\xf9y chỉnh"},{id:"custom_fields_create",name:"Tạo trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể tạo trường t\xf9y chỉnh mới"},{id:"custom_fields_edit",name:"Chỉnh sửa trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể chỉnh sửa cấu h\xecnh trường t\xf9y chỉnh"},{id:"custom_fields_delete",name:"X\xf3a trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể x\xf3a trường t\xf9y chỉnh"},{id:"custom_fields_manage",name:"Quản l\xfd to\xe0n bộ trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống trường t\xf9y chỉnh"},{id:"department_view",name:"Xem ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể xem danh s\xe1ch ph\xf2ng ban"},{id:"department_create",name:"Tạo ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể tạo ph\xf2ng ban mới"},{id:"department_edit",name:"Chỉnh sửa ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể chỉnh sửa th\xf4ng tin ph\xf2ng ban"},{id:"department_delete",name:"X\xf3a ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể x\xf3a ph\xf2ng ban"},{id:"department_member_manage",name:"Quản l\xfd th\xe0nh vi\xean ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể th\xeam, sửa, x\xf3a th\xe0nh vi\xean ph\xf2ng ban"},{id:"permissions_manage",name:"Quản l\xfd quyền",category:"Quản l\xfd quyền",description:"C\xf3 thể cấp v\xe0 thu hồi quyền của th\xe0nh vi\xean"},{id:"system_settings_view",name:"Xem c\xe0i đặt hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể xem c\xe0i đặt hệ thống"},{id:"system_settings_edit",name:"Chỉnh sửa c\xe0i đặt hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể chỉnh sửa c\xe0i đặt hệ thống"},{id:"system_logs_view",name:"Xem nhật k\xfd hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể xem nhật k\xfd hoạt động hệ thống"},{id:"system_admin_full_access",name:"Quyền admin to\xe0n quyền",category:"Quyền đặc biệt",description:"C\xf3 to\xe0n quyền truy cập hệ thống"},{id:"system_departments_manage",name:"Quản l\xfd hệ thống ph\xf2ng ban",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống ph\xf2ng ban"},{id:"system_users_manage",name:"Quản l\xfd hệ thống người d\xf9ng",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống người d\xf9ng"},{id:"system_settings_manage",name:"Quản l\xfd c\xe0i đặt hệ thống",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ c\xe0i đặt hệ thống"}],e=new Map;function f(a){let b=e.get(a);return b?b.name:a}d.forEach(a=>{e.set(a.id,a)})},31568:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(49754),e=c(9117),f=c(46595),g=c(32324),h=c(39326),i=c(38928),j=c(20175),k=c(12),l=c(54290),m=c(12696),n=c(82802),o=c(77533),p=c(45229),q=c(32822),r=c(261),s=c(26453),t=c(52474),u=c(26713),v=c(51356),w=c(62685),x=c(36225),y=c(63446),z=c(2762),A=c(45742),B=c(86439),C=c(81170),D=c.n(C),E=c(62506),F=c(91203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["(private)",{children:["dashboard",{children:["user",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,69585)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,87473)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,51472)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,5682)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,81170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.bind(c,59732)),"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,90461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,32768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(private)/dashboard/user/[id]/page",pathname:"/dashboard/user/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/(private)/dashboard/user/[id]/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66850:(a,b,c)=>{Promise.resolve().then(c.bind(c,69585))},69585:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\(private)\\\\dashboard\\\\user\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\(private)\\dashboard\\user\\[id]\\page.tsx","default")},71697:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(38301),e=c.n(d),f=c(7372),g=c.n(f);function h(){return(h=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var i=(0,d.forwardRef)(function(a,b){var c=a.color,d=a.size,f=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return e().createElement("svg",h({ref:b,xmlns:"http://www.w3.org/2000/svg",width:f,height:f,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),e().createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),e().createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),e().createElement("polyline",{points:"7 3 7 8 15 8"}))});i.propTypes={color:g().string,size:g().oneOfType([g().string,g().number])},i.displayName="Save";let j=i},73520:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>G});var d=c(21124),e=c(38301),f=c.n(e),g=c(1590),h=c(93758),i=c(51019),j=c(31980),k=c(86576),l=c(55550),m=c(42378);let n=({user:a,onSubmit:b,onSubmitPass:c})=>{let[n,o]=(0,e.useState)(!1);(0,m.useRouter)();let[p,q]=(0,e.useState)(null),r=["Male","Female","Not"],s=(0,j.mN)({resolver:(0,l.u)(k.aP),defaultValues:a||{_id:"",email:"",username:"",phonenumber:"",private:!1,gender:"Not",bio:"",permissions:[]}});f().useEffect(()=>{a&&(console.log("Resetting form with user data:",a),s.reset(a))},[a,s]),f().useEffect(()=>{console.log("Form state:",{isValid:s.formState.isValid,errors:s.formState.errors,values:s.getValues()}),Object.keys(s.formState.errors).length>0&&(console.log("Detailed validation errors:"),Object.entries(s.formState.errors).forEach(([a,b])=>{console.log(`Field "${a}":`,b)}))},[s.formState.isValid,s.formState.errors]);let t=(0,j.mN)({resolver:(0,l.u)(k.gS),defaultValues:{_id:a?._id||"",password:"",confirmPassword:""}});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.lV,{...s,children:(0,d.jsxs)("form",{onSubmit:s.handleSubmit(a=>{console.log("Form submitted with data:",a),console.log("Form errors:",s.formState.errors);let c={...a,phonenumber:a.phonenumber?.toString()||"",bio:a.bio||"",permissions:Array.isArray(a.permissions)?a.permissions:[]};console.log("Transformed data:",c),b(c)},a=>{console.log("Form validation errors:",a)}),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,d.jsx)(g.zB,{control:s.control,name:"username",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"User Name"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"username",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:s.control,name:"email",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Email"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"email",type:"email",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:s.control,name:"phonenumber",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Số điện thoại"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"Số điện thoại",type:"text",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:s.control,name:"gender",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Giới T\xednh"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("select",{...a,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,d.jsx)("option",{value:"",children:"Chọn giới t\xednh"}),r.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:s.control,name:"bio",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Tiểu sử"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)("textarea",{...a,rows:3,className:"w-full p-2 border border-gray-300 rounded-md"})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:s.control,name:"private",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Kho\xe1 Th\xe0nh vi\xean"}),(0,d.jsx)(g.MJ,{children:(0,d.jsxs)("div",{className:"flex flex-col space-y-3 mt-2",children:[(0,d.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,d.jsx)("input",{type:"radio",name:`private-${a.name}`,className:"w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3",value:"true",checked:!0===a.value,onChange:()=>a.onChange(!0)}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Kho\xe1 Kh\xe1ch h\xe0ng"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản sẽ bị v\xf4 hiệu h\xf3a"})]})]}),(0,d.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,d.jsx)("input",{type:"radio",name:`private-${a.name}`,className:"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3",value:"false",checked:!1===a.value,onChange:()=>a.onChange(!1)}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"font-medium text-gray-900",children:"Hoạt động"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản hoạt động b\xecnh thường"})]})]})]})}),(0,d.jsx)(g.C5,{})]})})]}),(0,d.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:p}),(0,d.jsx)("div",{className:"flex gap-4 justify-center mt-6",children:(0,d.jsxs)("button",{disabled:!!n,type:"submit",onClick:()=>{console.log("Submit button clicked"),console.log("Loading state:",n),console.log("Form valid:",s.formState.isValid),console.log("Form errors:",s.formState.errors)},className:"btn btn-primary bg-blue-700 w-40 text-white flex items-center",children:[n?(0,d.jsx)(i.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})})]})}),(0,d.jsx)(g.lV,{...t,children:(0,d.jsxs)("form",{onSubmit:t.handleSubmit(c),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8",noValidate:!0,children:[(0,d.jsx)(g.zB,{control:t.control,name:"password",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"Mật khẩu mới"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"password",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsx)(g.zB,{control:t.control,name:"confirmPassword",render:({field:a})=>(0,d.jsxs)(g.eI,{children:[(0,d.jsx)(g.lR,{children:"X\xe1c nhận mật khẩu"}),(0,d.jsx)(g.MJ,{children:(0,d.jsx)(h.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...a})}),(0,d.jsx)(g.C5,{})]})}),(0,d.jsxs)("button",{disabled:!!n,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[n?(0,d.jsx)(i.A,{className:"animate-spin"}):"","Update Password"]})]})})]})};var o=c(99420),p=c(80974),q=c(3991),r=c.n(q),s=c(74701),t=c(13909),u=c(71697),v=c(7372),w=c.n(v);function x(){return(x=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var y=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",x({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("circle",{cx:"12",cy:"12",r:"10"}),f().createElement("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),f().createElement("line",{x1:"9",y1:"9",x2:"15",y2:"15"}))});function z(){return(z=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}y.propTypes={color:w().string,size:w().oneOfType([w().string,w().number])},y.displayName="XCircle";var A=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",z({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("path",{d:"M12 20h9"}),f().createElement("path",{d:"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"}))});function B(){return(B=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}A.propTypes={color:w().string,size:w().oneOfType([w().string,w().number])},A.displayName="Edit3";var C=(0,e.forwardRef)(function(a,b){var c=a.color,d=a.size,e=void 0===d?24:d,g=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,["color","size"]);return f().createElement("svg",B({ref:b,xmlns:"http://www.w3.org/2000/svg",width:e,height:e,viewBox:"0 0 24 24",fill:"none",stroke:void 0===c?"currentColor":c,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},g),f().createElement("polyline",{points:"20 6 9 17 4 12"}))});C.propTypes={color:w().string,size:w().oneOfType([w().string,w().number])},C.displayName="Check";var D=c(20334),E=c(30756);function F({user:a,onPermissionsUpdate:b}){let[c,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)(a.permissions||[]),i=a.permissions||[],j="admin"===a.rule,k=E.v_.reduce((a,b)=>(a[b.category]||(a[b.category]=[]),a[b.category].push(b),a),{});return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(t.A,{className:"text-blue-500",size:24}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Quyền truy cập"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:j?"Quản trị vi\xean c\xf3 tất cả quyền":`${i.length} quyền được cấp`})]})]}),!j&&(0,d.jsx)("div",{className:"flex space-x-2",children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("button",{onClick:()=>{b&&b(a._id,g),f(!1)},className:"flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm",children:[(0,d.jsx)(u.A,{size:16}),(0,d.jsx)("span",{children:"Lưu"})]}),(0,d.jsxs)("button",{onClick:()=>{h(a.permissions||[]),f(!1)},className:"flex items-center space-x-1 px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm",children:[(0,d.jsx)(y,{size:16}),(0,d.jsx)("span",{children:"Hủy"})]})]}):(0,d.jsxs)("button",{onClick:()=>f(!0),className:"flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm",children:[(0,d.jsx)(A,{size:16}),(0,d.jsx)("span",{children:"Chỉnh sửa"})]})})]}),j?(0,d.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(t.A,{className:"text-blue-600",size:20}),(0,d.jsx)("span",{className:"text-blue-800 font-medium",children:"Quản trị vi\xean c\xf3 quyền truy cập tất cả c\xe1c t\xednh năng"})]})}):(0,d.jsx)("div",{className:"space-y-6",children:Object.entries(k).map(([a,b])=>(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:a}),(0,d.jsx)("div",{className:"space-y-2",children:b.map(a=>{let b=c?g.includes(a.id):i.includes(a.id);return(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-1",children:c?(0,d.jsx)("button",{onClick:()=>{var b;return b=a.id,void(g.includes(b)?h(g.filter(a=>a!==b)):h([...g,b]))},className:`w-5 h-5 rounded border-2 flex items-center justify-center ${b?"bg-green-500 border-green-500 text-white":"border-gray-300 hover:border-green-400"}`,children:b&&(0,d.jsx)(C,{size:12})}):(0,d.jsx)("div",{className:`w-5 h-5 rounded border-2 flex items-center justify-center ${b?"bg-green-500 border-green-500 text-white":"bg-red-100 border-red-300 text-red-500"}`,children:b?(0,d.jsx)(C,{size:12}):(0,d.jsx)(D.A,{size:12})})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:`font-medium ${b?"text-gray-900":"text-gray-500"}`,children:a.name}),(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${b?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:b?"C\xf3 quyền":"Kh\xf4ng c\xf3 quyền"})]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:a.description})]})]},a.id)})})]},a))})]})}function G({params:a}){let[b,c]=(0,e.useState)(null);(0,e.use)(a).id;let f=async a=>{try{console.log("Submitting user update data:",a);let b=localStorage.getItem("sessionToken")||"",d=await o.A.updateUser(a,b);console.log("Update result:",d),d.payload.success?(c(d.payload.user),p.oR.success("Cập nhật th\xe0nh c\xf4ng!")):(console.error("Error updating user:",d.payload),p.oR.error("Kh\xf4ng thể cập nhật: "+(d.payload?.message||"Lỗi kh\xf4ng x\xe1c định")))}catch(a){console.error("Unexpected error:",a),p.oR.error("C\xf3 lỗi xảy ra khi cập nhật. Vui l\xf2ng thử lại.")}},g=async a=>{try{console.log("Submitting password change data:",a);let b=localStorage.getItem("sessionToken")||"",c=await o.A.updatePassUser(a,b);console.log("Password change result:",c),c.payload.success?p.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng!"):(console.error("Error changing password:",c.payload),p.oR.error("Kh\xf4ng thể đổi mật khẩu: "+(c.payload?.message||"Lỗi kh\xf4ng x\xe1c định")))}catch(a){console.error("Unexpected error:",a),p.oR.error("C\xf3 lỗi xảy ra khi đổi mật khẩu. Vui l\xf2ng thử lại.")}},h=async(a,d)=>{try{let a=localStorage.getItem("sessionToken")||"",e={...b,permissions:d},f=await o.A.updateUser(e,a);f.payload.success?(c(f.payload.user),p.oR.success("Cập nhật quyền th\xe0nh c\xf4ng!")):(console.error("Error updating permissions:",f.payload),p.oR.error("Kh\xf4ng thể cập nhật quyền: "+(f.payload?.message||"Lỗi kh\xf4ng x\xe1c định")))}catch(a){console.error("Unexpected error:",a),p.oR.error("C\xf3 lỗi xảy ra khi cập nhật quyền. Vui l\xf2ng thử lại.")}};return(0,d.jsx)(s.default,{requiredPermission:"user_edit",children:(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa t\xe0i khoản"}),(0,d.jsx)(r(),{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",href:`/dashboard/user/log/${b?._id}`,children:"Xem User log"})]}),b?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,d.jsx)(n,{onSubmit:f,onSubmitPass:g,user:b})]}),(0,d.jsx)(F,{user:b,onPermissionsUpdate:h})]}):(0,d.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,d.jsx)("p",{className:"mt-4 text-gray-500",children:"Đang tải th\xf4ng tin..."})]})})]})})}},74701:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(21124),e=c(25816),f=c(42378);function g({children:a,requiredPermission:b,requiredPermissions:c=[],requireAll:g=!1,fallbackPath:h="/dashboard"}){let{hasPermission:i,hasAnyPermission:j,isAdmin:k,isDepartmentManager:l,isLoading:m}=(0,e.S)(),n=(0,f.useRouter)();if(m)return(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(k)return(0,d.jsx)(d.Fragment,{children:a});return(b?"admin"===b&&!!l||i(b):!(c.length>0)||(g?c.every(a=>i(a)):j(c)))?(0,d.jsx)(d.Fragment,{children:a}):(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,d.jsx)("button",{onClick:()=>n.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}c(38301)},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86576:(a,b,c)=>{"use strict";c.d(b,{PD:()=>f,aP:()=>g,gS:()=>h});var d=c(65923);let e=d.Ik({_id:d.Yj(),username:d.Yj(),phonenumber:d.ai(),email:d.Yj().email(),createdAt:d.Yj().date(),private:d.zM(),rule:d.Yj()});d.Ik({total:d.ai(),users:d.YO(e)});let f=d.Ik({username:d.Yj().trim().min(2).max(256),email:d.Yj().email(),password:d.Yj().min(6).max(100),phonenumber:d.Yj(),department:d.Yj().optional(),permissions:d.YO(d.Yj()).optional()}).strict(),g=d.Ik({_id:d.Yj(),username:d.Yj().trim().min(2).max(256),email:d.Yj().email(),phonenumber:d.bz(),private:d.zM(),rule:d.k5(["user","admin","manager","editor"]).optional(),rank:d.Yj().optional(),gender:d.k5(["Male","Female","Not"]),bio:d.bz(),permissions:d.YO(d.Yj())}),h=d.Ik({_id:d.Yj(),password:d.Yj().min(6,"Mật khẩu phải c\xf3 \xedt nhất 6 k\xfd tự"),confirmPassword:d.Yj()}).refine(a=>a.password===a.confirmPassword,{message:"Mật khẩu x\xe1c nhận kh\xf4ng khớp",path:["confirmPassword"]}),i=d.Ik({_id:d.Yj(),user:d.Yj(),ip:d.Yj(),device:d.Yj(),loginTime:d.Yj().datetime(),logoutTime:d.Yj().datetime().nullable()});d.Ik({logs:d.YO(i)})},93758:(a,b,c)=>{"use strict";c.d(b,{p:()=>i});var d=c(21124),e=c(44943),f=c(29411),g=c(58015),h=c(38301);let i=c.n(h)().forwardRef(({className:a,type:b,...c},i)=>{let[j,k]=(0,h.useState)(!1);return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"relative w-full",children:[(0,d.jsx)("input",{type:"password"===b&&j?"text":b,autoComplete:"password"===b?"new-password":"",className:(0,e.cn)("input input-bordered w-full rounded-md",a),ref:i,...c}),"password"===b&&(j?(0,d.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}):(0,d.jsx)(g.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>k(!j)}))]})})});i.displayName="Input"},99420:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={fetchUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),getAllUsers:(a,b)=>d.Ay.post("/api/administrator/users",a,{headers:{Authorization:`Bearer ${b}`}}),fetchLogs:(a,b)=>d.Ay.get(`api/administrator/log/${a}`,{headers:{Authorization:`Bearer ${b}`}}),deleteUser:(a,b)=>d.Ay.delete(`api/administrator/users/${a._id}`,{headers:{Authorization:`Bearer ${b}`}}),fetchUserById:(a,b,c)=>d.Ay.get(`api/administrator/users/${a}`,{headers:{Authorization:`Bearer ${b}`},signal:c}),CreateUser:(a,b)=>d.Ay.post("api/administrator/signup",a,{headers:{Authorization:`Bearer ${b}`}}),updateUser:(a,b)=>d.Ay.put("api/administrator/change-info/",a,{headers:{Authorization:`Bearer ${b}`}}),updatePassUser:(a,b)=>d.Ay.put("api/administrator/users/change-pass/",a,{headers:{Authorization:`Bearer ${b}`}})}}};var b=require("../../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,1428,4479,6975,3131,3445,3271],()=>b(b.s=31568));module.exports=c})();