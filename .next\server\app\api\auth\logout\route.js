(()=>{var a={};a.id=8489,a.ids=[8489],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3421:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(71237),e=c(55088),f=c(17679);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30787:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return i}});let d=function(a,b){if(a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=e(b);if(c&&c.has(a))return c.get(a);var d={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(d,g,h):d[g]=a[g]}return d.default=a,c&&c.set(a,d),d}(c(74515));function e(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(e=function(a){return a?c:b})(a)}let f={current:null},g="function"==typeof d.cache?d.cache:a=>a,h=console.warn;function i(a){return function(...b){h(a(...b))}}g(a=>{try{h(f.current)}finally{f.current=null}})},31716:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{isRequestAPICallableInsideAfter:function(){return i},throwForSearchParamsAccessInUseCache:function(){return h},throwWithStaticGenerationBailoutError:function(){return f},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return g}});let d=c(11938),e=c(3295);function f(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function g(a,b){throw Object.defineProperty(new d.StaticGenBailoutError(`Route ${a} with \`dynamic = "error"\` couldn't be rendered statically because it used ${b}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function h(a,b){let c=Object.defineProperty(Error(`Route ${a.route} used "searchParams" inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await "searchParams" outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E779",enumerable:!1,configurable:!0});throw Error.captureStackTrace(c,b),a.invalidDynamicUsageError??=c,c}function i(){let a=e.afterTaskAsyncStorage.getStore();return(null==a?void 0:a.rootTaskSpawnPhase)==="action"}},31977:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{POST:()=>x});var e=c(95736),f=c(9117),g=c(4044),h=c(39326),i=c(32324),j=c(261),k=c(54290),l=c(85328),m=c(38928),n=c(46595),o=c(3421),p=c(17679),q=c(41681),r=c(63446),s=c(86439),t=c(51356),u=c(35646);c(67412).config();let v={logoutFromNextServerToServer:a=>u.Ay.post("/api/auth/blacklist-token/",a,{headers:{Authorization:`Bearer ${a.sessionToken}`}})};var w=c(86802);async function x(a){if((await a.json()).force)return Response.json({message:"Buộc đăng xuất th\xe0nh c\xf4ng"},{status:200,headers:{"Set-Cookie":["sessionToken=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax","userRole=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax","sessionTokenExpiresAt=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax"]}});let b=(0,w.UL)(),c=b.get("sessionToken");if(b.get("sessionTokenExpiresAt"),!c)return Response.json({message:"Kh\xf4ng nhận được session token"},{status:401});try{let a=await v.logoutFromNextServerToServer({sessionToken:c.value});return Response.json(a.payload,{status:200,headers:{"Set-Cookie":["sessionToken=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax","userRole=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax","sessionTokenExpiresAt=; Path=/; HttpOnly; Max-Age=0; SameSite=Lax"]}})}catch(a){if(a instanceof u.j$)return Response.json(a.payload,{status:a.status});return Response.json({message:"Lỗi kh\xf4ng x\xe1c định"},{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/auth/logout/route",pathname:"/api/auth/logout",filename:"route",bundlePath:"app/api/auth/logout/route"},distDir:".next",relativeProjectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\api\\auth\\logout\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/auth/logout/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:!1});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},33873:a=>{"use strict";a.exports=require("path")},35646:(a,b,c)=>{"use strict";c.d(b,{j$:()=>f,Ay:()=>j});var d=c(47960);c(48318);var e=c(82161);class f extends Error{constructor({status:a,payload:b}){super("Http Error"),this.status=a,this.payload=b}}class g extends f{constructor({status:a,payload:b}){super({status:a,payload:b}),this.status=a,this.payload=b}}let h=null,i=async(a,b,c)=>{let i;c?.body instanceof FormData?i=c.body:c?.body&&(i=JSON.stringify(c.body));let j=i instanceof FormData?{}:{"Content-Type":"application/json"},k=c?.baseUrl===void 0?d.A.NEXT_PUBLIC_API_ENDPOINT:c.baseUrl,l=b.startsWith("/")?`${k}${b}`:`${k}/${b}`,m=await fetch(l,{...c,headers:{...j,...c?.headers},body:i,method:a}),n=null,o=m.headers.get("content-type");if(o&&o.includes("application/json"))try{n=await m.json()}catch(a){console.error("Failed to parse JSON response:",a),n=null}else n=await m.text();let p={status:m.status,payload:n};if(!m.ok)if(404===m.status||403===m.status)throw new g(p);else if(401===m.status){if(1){let a="";{let b=c?.headers?.Authorization;b?.startsWith("Bearer ")&&(a=b.split("Bearer ")[1])}(0,e.redirect)(`/logout?sessionToken=${a}`)}else if(!h){h=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...j}});try{let a=async a=>{if("http://localhost:3000"!==a.origin)return};window.addEventListener("message",a),await h}catch(a){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),h=null,location.href="/login"}}}else throw new f(p);return p},j={get:(a,b)=>i("GET",a,b),post:(a,b,c)=>i("POST",a,{...c,body:b}),put:(a,b,c)=>i("PUT",a,{...c,body:b}),patch:(a,b,c)=>i("PATCH",a,{...c,body:b}),delete:(a,b)=>i("DELETE",a,{...b})}},43740:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isPostpone",{enumerable:!0,get:function(){return d}});let c=Symbol.for("react.postpone");function d(a){return"object"==typeof a&&null!==a&&a.$$typeof===c}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47960:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(45711);let e=d.Ik({NEXT_PUBLIC_API_ENDPOINT:d.Yj().url(),NEXT_PUBLIC_URL:d.Yj().url(),CRYPTOJS_SECRECT:d.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:process.env.CRYPTOJS_SECRECT});if(!e.success)throw console.error("Invalid environment variables:",e.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let f=e.data},55511:a=>{"use strict";a.exports=require("crypto")},61981:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isNextRouterError",{enumerable:!0,get:function(){return f}});let d=c(98541),e=c(92781);function f(a){return(0,e.isRedirectError)(a)||(0,d.isHTTPAccessFallbackError)(a)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67412:(a,b,c)=>{let d=c(29021),e=c(33873),f=c(21820),g=c(55511),h=c(77336).version,i=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function j(a){console.log(`[dotenv@${h}][DEBUG] ${a}`)}function k(a){console.log(`[dotenv@${h}] ${a}`)}function l(a){return a&&a.DOTENV_KEY&&a.DOTENV_KEY.length>0?a.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function m(a){let b=null;if(a&&a.path&&a.path.length>0)if(Array.isArray(a.path))for(let c of a.path)d.existsSync(c)&&(b=c.endsWith(".vault")?c:`${c}.vault`);else b=a.path.endsWith(".vault")?a.path:`${a.path}.vault`;else b=e.resolve(process.cwd(),".env.vault");return d.existsSync(b)?b:null}function n(a){return"~"===a[0]?e.join(f.homedir(),a.slice(1)):a}let o={configDotenv:function(a){let b,c=e.resolve(process.cwd(),".env"),f="utf8",g=!!(a&&a.debug),h=!a||!("quiet"in a)||a.quiet;a&&a.encoding?f=a.encoding:g&&j("No encoding is specified. UTF-8 is used by default");let i=[c];if(a&&a.path)if(Array.isArray(a.path))for(let b of(i=[],a.path))i.push(n(b));else i=[n(a.path)];let l={};for(let c of i)try{let b=o.parse(d.readFileSync(c,{encoding:f}));o.populate(l,b,a)}catch(a){g&&j(`Failed to load ${c} ${a.message}`),b=a}let m=process.env;if(a&&null!=a.processEnv&&(m=a.processEnv),o.populate(m,l,a),g||!h){let a=Object.keys(l).length,c=[];for(let a of i)try{let b=e.relative(process.cwd(),a);c.push(b)}catch(c){g&&j(`Failed to load ${a} ${c.message}`),b=c}k(`injecting env (${a}) from ${c.join(",")}`)}return b?{parsed:l,error:b}:{parsed:l}},_configVault:function(a){let b=!!(a&&a.debug),c=!a||!("quiet"in a)||a.quiet;(b||!c)&&k("Loading env from encrypted .env.vault");let d=o._parseVault(a),e=process.env;return a&&null!=a.processEnv&&(e=a.processEnv),o.populate(e,d,a),{parsed:d}},_parseVault:function(a){let b,c=m(a=a||{});a.path=c;let d=o.configDotenv(a);if(!d.parsed){let a=Error(`MISSING_DATA: Cannot parse ${c} for an unknown reason`);throw a.code="MISSING_DATA",a}let e=l(a).split(","),f=e.length;for(let a=0;a<f;a++)try{let c=e[a].trim(),f=function(a,b){let c;try{c=new URL(b)}catch(a){if("ERR_INVALID_URL"===a.code){let a=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw a.code="INVALID_DOTENV_KEY",a}throw a}let d=c.password;if(!d){let a=Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let e=c.searchParams.get("environment");if(!e){let a=Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let f=`DOTENV_VAULT_${e.toUpperCase()}`,g=a.parsed[f];if(!g){let a=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${f} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:g,key:d}}(d,c);b=o.decrypt(f.ciphertext,f.key);break}catch(b){if(a+1>=f)throw b}return o.parse(b)},config:function(a){if(0===l(a).length)return o.configDotenv(a);let b=m(a);if(!b){var c;return c=`You set DOTENV_KEY but you are missing a .env.vault file at ${b}. Did you forget to build it?`,console.log(`[dotenv@${h}][WARN] ${c}`),o.configDotenv(a)}return o._configVault(a)},decrypt:function(a,b){let c=Buffer.from(b.slice(-64),"hex"),d=Buffer.from(a,"base64"),e=d.subarray(0,12),f=d.subarray(-16);d=d.subarray(12,-16);try{let a=g.createDecipheriv("aes-256-gcm",c,e);return a.setAuthTag(f),`${a.update(d)}${a.final()}`}catch(d){let a=d instanceof RangeError,b="Invalid key length"===d.message,c="Unsupported state or unable to authenticate data"===d.message;if(a||b){let a=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw a.code="INVALID_DOTENV_KEY",a}if(c){let a=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw a.code="DECRYPTION_FAILED",a}throw d}},parse:function(a){let b,c={},d=a.toString();for(d=d.replace(/\r\n?/mg,"\n");null!=(b=i.exec(d));){let a=b[1],d=b[2]||"",e=(d=d.trim())[0];d=d.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===e&&(d=(d=d.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),c[a]=d}return c},populate:function(a,b,c={}){let d=!!(c&&c.debug),e=!!(c&&c.override);if("object"!=typeof b){let a=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw a.code="OBJECT_REQUIRED",a}for(let c of Object.keys(b))Object.prototype.hasOwnProperty.call(a,c)?(!0===e&&(a[c]=b[c]),d&&(!0===e?j(`"${c}" is already defined and WAS overwritten`):j(`"${c}" is already defined and was NOT overwritten`))):a[c]=b[c]}};a.exports.configDotenv=o.configDotenv,a.exports._configVault=o._configVault,a.exports._parseVault=o._parseVault,a.exports.config=o.config,a.exports.decrypt=o.decrypt,a.exports.parse=o.parse,a.exports.populate=o.populate,a.exports=o},77336:a=>{"use strict";a.exports=JSON.parse('{"name":"dotenv","version":"16.6.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},92781:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{REDIRECT_ERROR_CODE:function(){return e},RedirectType:function(){return f},isRedirectError:function(){return g}});let d=c(91203),e="NEXT_REDIRECT";var f=function(a){return a.push="push",a.replace="replace",a}({});function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,f]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===e&&("replace"===f||"push"===f)&&"string"==typeof g&&!isNaN(h)&&h in d.RedirectStatusCode}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},95736:(a,b,c)=>{"use strict";a.exports=c(44870)},96487:()=>{},98541:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{HTTPAccessErrorStatus:function(){return c},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return e},getAccessFallbackErrorTypeByStatus:function(){return h},getAccessFallbackHTTPStatus:function(){return g},isHTTPAccessFallbackError:function(){return f}});let c={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},d=new Set(Object.values(c)),e="NEXT_HTTP_ERROR_FALLBACK";function f(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,c]=a.digest.split(";");return b===e&&d.has(Number(c))}function g(a){return Number(a.digest.split(";")[1])}function h(a){switch(a){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[5873,5711,1503],()=>b(b.s=31977));module.exports=c})();