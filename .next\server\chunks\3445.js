exports.id=3445,exports.ids=[3445],exports.modules={924:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,3991,23))},3412:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(21124);c(38301);var e=c(42378),f=c(21884);let g=()=>{let{setting:a,menus:b}=(0,f.i)(),c=(0,e.usePathname)().startsWith("/dashboard"),g=b?.find(a=>"7"===a.position);return(0,d.jsx)(d.Fragment,{children:!c&&(0,d.jsxs)("footer",{className:"mx-auto border-t border-gray-300",children:[(0,d.jsx)("div",{className:"bg-[#e4393c] text-white",children:(0,d.jsx)("div",{className:"container mx-auto px-6 py-8",children:(0,d.jsxs)("div",{className:"copyright flex flex-wrap items-center justify-between",children:[(0,d.jsx)("div",{className:"mb-4 md:mb-0",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"mr-3",children:(0,d.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",className:"w-12 h-12"})}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("div",{className:"text-sm text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,d.jsx)("div",{className:"text-xl font-bold",children:"T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH"})]})]})}),g&&(0,d.jsx)("div",{className:"right flex flex-wrap gap-4",children:g.obj.map(a=>(0,d.jsx)("a",{href:`/${a.slug}`,className:"hover:text-blue-200 transition-colors",children:a.text},a.id))})]})})}),(0,d.jsx)("div",{className:"bg-[#ca1619]",children:(0,d.jsx)("div",{className:"container mx-auto px-6 py-8",children:(0,d.jsxs)("div",{className:"copyright-footer grid md:grid-cols-3 grid-cols-1 gap-8",children:[a?.footerBLock1&&(0,d.jsx)("div",{className:"text-white font-normal",children:(0,d.jsx)("div",{className:"footer-content",dangerouslySetInnerHTML:{__html:a.footerBLock1}})}),a?.footerBLock2&&(0,d.jsx)("div",{className:"text-white font-normal",children:(0,d.jsx)("div",{className:"footer-content",dangerouslySetInnerHTML:{__html:a.footerBLock2}})}),(0,d.jsx)("div",{className:"flex items-center justify-start md:justify-end",children:(0,d.jsx)("p",{className:"text-white font-normal",children:a?.copyright||"\xa9 Your Website Name"})})]})})})]})})}},5085:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(21124),e=c(17017),f=c(89869),g=c(42378),h=c(80974),i=c(97841);function j({compact:a=!1}){let{setUser:b}=(0,f.U)();(0,g.useRouter)(),(0,g.usePathname)();let c=async()=>{try{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),localStorage.removeItem("sessionTokenExpiresAt"),await e.A.logoutFromNextClientToNextServer(),b(null),h.oR.success("Đăng xuất th\xe0nh c\xf4ng!"),window.location.href="/login?logout=true"}catch(a){console.error("Logout error:",a);try{await e.A.logoutFromNextClientToNextServer(!0)}catch(a){console.error("Force logout error:",a)}localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),localStorage.removeItem("sessionTokenExpiresAt"),b(null),window.location.href="/login?force=true"}};return(0,d.jsxs)("button",{className:`
        flex items-center justify-center gap-2 px-4 py-2 
        bg-red-500 hover:bg-red-600 text-white text-sm font-medium 
        rounded-lg transition-colors duration-200
        ${a?"w-10 h-10 p-0":"w-full"}
      `,onClick:c,title:a?"Đăng xuất":"",children:[(0,d.jsx)(i.A,{size:18}),!a&&(0,d.jsx)("span",{children:"Đăng xuất"})]})}},5682:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\error.tsx","default")},14005:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,81170,23)),Promise.resolve().then(c.t.bind(c,23597,23)),Promise.resolve().then(c.t.bind(c,36893,23)),Promise.resolve().then(c.t.bind(c,89748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,69576,23)),Promise.resolve().then(c.t.bind(c,73041,23)),Promise.resolve().then(c.t.bind(c,51384,23))},14492:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,65169,23))},17017:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);c(50426).config();let e={login:a=>d.Ay.post("/api/auth/login",a),register:a=>d.Ay.post("/api/auth/signup",a),forgot:a=>d.Ay.put("/api/auth/app-forgot-pass",a),changepass:a=>d.Ay.put("/api/auth/app-reset-pass",a),auth:a=>d.Ay.post("/api/auth",a,{baseUrl:""}),checkCode:a=>d.Ay.get(`/api/auth/check-code/${a}`),VerifyAppCode:a=>d.Ay.post("/api/auth/verify-app-code",a),VerifyCode:a=>d.Ay.post("/api/auth/verify-code",a),logoutFromNextServerToServer:a=>d.Ay.post("/api/auth/blacklist-token/",a,{headers:{Authorization:`Bearer ${a.sessionToken}`}}),logoutFromNextClientToNextServer:(a,b)=>d.Ay.post("/api/auth/logout",{force:a},{baseUrl:"",signal:b}),slideSessionFromNextServerToServer:a=>d.Ay.post("/auth/slide-session",{},{headers:{Authorization:`Bearer ${a}`}}),slideSessionFromNextClientToNextServer:()=>d.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},21884:(a,b,c)=>{"use strict";c.d(b,{SettingProvider:()=>k,i:()=>l});var d=c(21124),e=c(38301),f=c(48310),g=c(42378);let h=(a,b)=>{try{localStorage.setItem(a,JSON.stringify(b)),localStorage.setItem(`${a}_timestamp`,Date.now().toString())}catch(a){console.error("❌ Error setting cache with timestamp:",a)}},i=(a,b=30)=>{try{if(((a,b=30)=>{try{let c=localStorage.getItem(`${a}_timestamp`);if(!c)return!0;let d=parseInt(c);return Date.now()-d>60*b*1e3}catch(a){return console.error("❌ Error checking cache staleness:",a),!0}})(a,b))return null;let c=localStorage.getItem(a);return c?JSON.parse(c):null}catch(a){return console.error("❌ Error getting fresh cache:",a),null}},j=(0,e.createContext)(void 0),k=({children:a})=>{let[b,c]=(0,e.useState)(null),[k,l]=(0,e.useState)(null),[m,n]=(0,e.useState)(!0),o=(0,g.usePathname)(),p=async(a=!1)=>{try{if(n(!0),!a){let a=i("siteSetting",5),b=i("siteMenus",5);if(a&&b){c(a),l(b),n(!1);return}}let b=await f.A.commonFetchSetting();b.payload.success?(c(b.payload.setting),l(b.payload.menus),h("siteSetting",b.payload.setting),h("siteMenus",b.payload.menus)):console.error("Failed to fetch settings")}catch(a){console.error("Error fetching settings:",a)}finally{n(!1)}};return(0,e.useEffect)(()=>{if("/"===o||o.startsWith("/dashboard"))p();else{let a=i("siteSetting",30),b=i("siteMenus",30);a&&b?(c(a),l(b),n(!1)):p()}},[o]),(0,d.jsx)(j.Provider,{value:{setting:b,loading:m,menus:k,refreshSettings:()=>{console.log("\uD83D\uDD04 Force refreshing settings...");try{localStorage.removeItem("siteSetting"),localStorage.removeItem("siteMenus"),console.log("✅ Setting cache cleared")}catch(a){console.error("❌ Error clearing setting cache:",a)}p(!0)}},children:a})},l=()=>{let a=(0,e.useContext)(j);if(!a)throw Error("useSetting must be used within a SettingProvider");return a}},24699:(a,b,c)=>{Promise.resolve().then(c.bind(c,42480)),Promise.resolve().then(c.t.bind(c,87537,23)),Promise.resolve().then(c.bind(c,97036)),Promise.resolve().then(c.bind(c,99815)),Promise.resolve().then(c.bind(c,91492)),Promise.resolve().then(c.bind(c,64074)),Promise.resolve().then(c.bind(c,59836)),Promise.resolve().then(c.bind(c,73338)),Promise.resolve().then(c.bind(c,91683))},27573:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,54160,23)),Promise.resolve().then(c.t.bind(c,31603,23)),Promise.resolve().then(c.t.bind(c,68495,23)),Promise.resolve().then(c.t.bind(c,75170,23)),Promise.resolve().then(c.t.bind(c,77526,23)),Promise.resolve().then(c.t.bind(c,78922,23)),Promise.resolve().then(c.t.bind(c,29234,23)),Promise.resolve().then(c.t.bind(c,12263,23)),Promise.resolve().then(c.bind(c,82146))},44943:(a,b,c)=>{"use strict";c.d(b,{Fd:()=>g,cn:()=>f}),c(65424);var d=c(43249),e=c(58829);function f(...a){return(0,e.QP)((0,d.$)(a))}c(98616);let g=a=>a.startsWith("/")?a.slice(1):a},48310:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(65424);let e={fetchSetting:a=>d.Ay.get("api/setting/admin",{headers:{Authorization:`Bearer ${a}`}}),commonFetchSetting:()=>d.Ay.get("api/setting/"),CraeteSetting:(a,b)=>d.Ay.put("api/setting/",a,{headers:{Authorization:`Bearer ${b}`}}),EditorSetting:(a,b)=>d.Ay.put("api/setting/editor",a,{headers:{Authorization:`Bearer ${b}`}}),CraeteMenu:(a,b)=>d.Ay.post("api/menu/",a,{headers:{Authorization:`Bearer ${b}`}}),EditMenu:(a,b)=>d.Ay.put("api/menu/edit",a,{headers:{Authorization:`Bearer ${b}`}}),GetMenu:a=>d.Ay.get(`api/menu/${a}`),fetchMenus:(a,b)=>d.Ay.post("api/menu/get-all",a,{headers:{Authorization:`Bearer ${b}`}}),deleteMenu:(a,b)=>d.Ay.delete(`api/menu/${a._id}`,{headers:{Authorization:`Bearer ${b}`}})}},50886:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(89869);let e={admin:["admin","editor","manager","user","department_manager","department_member"],editor:["editor","user"],manager:["manager","editor","user"],department_manager:["department_manager","user"],department_member:["department_member","user"],user:["user"]},f=a=>["admin","editor","manager","user","department_manager","department_member"].includes(a),g=()=>{let{user:a}=(0,d.U)();return{user:a,isAuthenticated:!!a,hasPermission:b=>!!(a&&f(a.rule))&&((a,b)=>!!f(a)&&(e[a]?.includes(b)??!1))(a.rule,b)}}},51472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>t,metadata:()=>s});var d=c(75338);c(72547);var e=c(68454),f=c.n(e),g=c(19870),h=c.n(g),i=c(59836),j=c(64074),k=c(99815),l=c(87537),m=c.n(l),n=c(42480),o=c(97036);c(56070);var p=c(91683),q=c(73338),r=c(91492);let s={title:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH",description:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH"};async function t({children:a}){return(0,d.jsxs)("html",{lang:"en",className:`${f().className} ${h().className}`,"data-theme":"light",children:[(0,d.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, minimal-ui, shrink-to-fit=no, viewport-fit=cover"}),(0,d.jsxs)("head",{children:[(0,d.jsx)("meta",{name:"application-name",content:""}),(0,d.jsx)("meta",{name:"mobile-web-app-capable",content:"yes"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,d.jsx)("meta",{name:"apple-mobile-web-app-title",content:"B\xe1o VnExpress - B\xe1o tiếng Việt nhiều người xem nhất"}),(0,d.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:`
            /* Override user agent stylesheet for ::marker */
            ::marker {
              unicode-bidi: normal !important;
              font-variant-numeric: normal !important;
              text-transform: inherit !important;
              text-indent: inherit !important;
              text-align: inherit !important;
              text-align-last: inherit !important;
              content: none !important;
              display: none !important;
            }

            ul, ol {
              list-style: none !important;
            }

            ul::marker, ol::marker, li::marker {
              content: none !important;
              display: none !important;
            }
          `}})]}),(0,d.jsxs)("body",{className:"overflow-x-hidden antialiased w-screen bg-white text-gray-900",children:[(0,d.jsx)(q.default,{}),(0,d.jsx)(n.GoogleOAuthProvider,{clientId:process.env.GOOGLE_CLIENT_ID||"",children:(0,d.jsx)(k.default,{children:(0,d.jsxs)(p.SettingProvider,{children:[(0,d.jsx)(r.default,{}),(0,d.jsx)(o.ToastContainer,{position:"bottom-right",autoClose:5e3,hideProgressBar:!1,newestOnTop:!1,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:"light"}),(0,d.jsx)(i.default,{}),(0,d.jsxs)("div",{className:"main bg-white text-gray-900",children:[(0,d.jsx)(m(),{}),a]}),(0,d.jsx)(j.default,{})]})})})]})]})}},59732:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(75338),e=c(65169),f=c.n(e);function g(){return(0,d.jsxs)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold",children:"404 - Page Not Found"}),(0,d.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Oops! This page doesn't exist."}),(0,d.jsx)(f(),{href:"/",className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Về Trang Chủ"})]})}},59836:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\Header.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Header.tsx","default")},64074:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Footer.tsx","default")},65424:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>j});var d=c(88034),e=(c(44943),c(42378));class f extends Error{constructor({status:a,payload:b}){super("Http Error"),this.status=a,this.payload=b}}class g extends f{constructor({status:a,payload:b}){super({status:a,payload:b}),this.status=a,this.payload=b}}let h=null,i=async(a,b,c)=>{let i;c?.body instanceof FormData?i=c.body:c?.body&&(i=JSON.stringify(c.body));let j=i instanceof FormData?{}:{"Content-Type":"application/json"},k=c?.baseUrl===void 0?d.A.NEXT_PUBLIC_API_ENDPOINT:c.baseUrl,l=b.startsWith("/")?`${k}${b}`:`${k}/${b}`,m=await fetch(l,{...c,headers:{...j,...c?.headers},body:i,method:a}),n=null,o=m.headers.get("content-type");if(o&&o.includes("application/json"))try{n=await m.json()}catch(a){console.error("Failed to parse JSON response:",a),n=null}else n=await m.text();let p={status:m.status,payload:n};if(!m.ok)if(404===m.status||403===m.status)throw new g(p);else if(401===m.status){if(1){let a="";{let b=c?.headers?.Authorization;b?.startsWith("Bearer ")&&(a=b.split("Bearer ")[1])}(0,e.redirect)(`/logout?sessionToken=${a}`)}else if(!h){h=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...j}});try{let a=async a=>{if("http://localhost:3000"!==a.origin)return};window.addEventListener("message",a),await h}catch(a){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),h=null,location.href="/login"}}}else throw new f(p);return p},j={get:(a,b)=>i("GET",a,b),post:(a,b,c)=>i("POST",a,{...c,body:b}),put:(a,b,c)=>i("PUT",a,{...c,body:b}),patch:(a,b,c)=>i("PATCH",a,{...c,body:b}),delete:(a,b)=>i("DELETE",a,{...b})}},72547:()=>{},72649:(a,b,c)=>{"use strict";c.d(b,{default:()=>s});var d=c(21124),e=c(42378),f=c(38301),g=c(20334),h=c(3991),i=c.n(h),j=c(21884),k=c(85351),l=c(91211),m=c(69256);function n(){let[a,b]=(0,f.useState)(""),c=(0,e.useRouter)();return(0,d.jsxs)("form",{className:"mx-auto",onSubmit:d=>{d.preventDefault(),a.trim()&&(c.push(`/search?q=${encodeURIComponent(a)}`),b(""))},children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-900 sr-only dark:text-white",children:"Search"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-y-0 start-0 flex items-center ps-3 pointer-events-none",children:(0,d.jsx)(m.A,{className:"w-4 h-4 text-gray-500 dark:text-gray-400"})}),(0,d.jsx)("input",{type:"search",id:"default-search",className:"block w-full rounded-xl p-2 ps-8 text-sm text-gray-900 border border-gray-300 bg-gray-50 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500",placeholder:"T\xecm kiếm",value:a,onChange:a=>b(a.target.value),required:!0})]})]})}let o=()=>{let{menus:a}=(0,j.i)(),[b,c]=(0,f.useState)(!1);if((0,f.useEffect)(()=>{let a=()=>{let a=document.getElementById("nav");if(!a)return;let b=a.offsetTop+a.offsetHeight;(window.scrollY||window.pageYOffset)>=b?c(!0):c(!1)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]),!a)return(0,d.jsx)("div",{className:"border-t border-red-800 bottom-nav text-white",style:{backgroundColor:"#e4393c"},children:(0,d.jsx)("div",{className:"container mx-auto",children:(0,d.jsx)("nav",{id:"nav",className:`w-full ${b?"sticky top-0 shadow-md z-50":""}`,style:{backgroundColor:"#e4393c"},children:(0,d.jsx)("div",{className:"flex w-full animate-pulse",children:Array.from({length:6}).map((a,b)=>(0,d.jsx)("div",{className:"flex-1 border-r border-red-800 last:border-r-0 h-10 py-2 px-4",children:(0,d.jsx)("div",{className:"h-full bg-red-700 rounded"})},b))})})})});if(!Array.isArray(a))return null;let e=a.find(a=>"0"===a.position);if(!e||!e.obj)return null;let g=e.obj.reduce((a,b)=>(a[b.parent]=a[b.parent]||[],a[b.parent].push(b),a),{}),h=(a,b=!1)=>g[a]?b?(0,d.jsx)("ul",{className:"sub-menu dropdown-content text-white z-[1] w-52 p-2 shadow rounded border-t border-red-700",style:{backgroundColor:"#d13438"},children:g[a].map(a=>(0,d.jsx)("li",{className:"relative my-2 cursor-pointer hover:bg-red-800",children:g[a.id]?(0,d.jsxs)("div",{className:"dropdown dropdown-bottom dropdown-hover",children:[(0,d.jsxs)("div",{tabIndex:0,className:"cursor-pointer flex items-center",children:[(0,d.jsx)(i(),{href:`/${a.slug}`,className:"block w-full",children:(0,d.jsx)("span",{className:"whitespace-nowrap uppercase",children:a.text})}),(0,d.jsx)(k.A,{className:"w-4 h-4 text-white ml-1 hidden md:flex",size:"12"})]}),h(a.id,!0)]}):(0,d.jsx)(i(),{href:`/${a.slug}`,className:"block w-full",children:(0,d.jsx)("span",{className:"whitespace-nowrap uppercase",children:a.text})})},a.id))}):(0,d.jsxs)("div",{className:"flex w-full",children:[(0,d.jsx)(i(),{href:"/",className:"flex-none border-r",style:{backgroundColor:"#e4393c"},children:(0,d.jsx)("div",{className:"flex justify-center items-center h-full px-4 py-2",children:(0,d.jsx)(l.A,{size:"18",className:"text-white"})})}),g[a].map(a=>(0,d.jsx)("div",{className:"text-center border-r",style:{backgroundColor:"#e4393c"},children:g[a.id]?(0,d.jsxs)("div",{className:"dropdown dropdown-bottom dropdown-hover h-full",children:[(0,d.jsxs)("div",{tabIndex:0,className:"h-full cursor-pointer flex justify-center items-center px-2 py-2 hover:bg-red-700",children:[(0,d.jsx)(i(),{href:`/${a.slug}`,className:"block",children:(0,d.jsx)("span",{className:"whitespace-nowrap uppercase text-white text-sm font-medium",children:a.text})}),(0,d.jsx)(k.A,{className:"w-4 h-4 text-white ml-1 hidden md:flex",size:"12"})]}),h(a.id,!0)]}):(0,d.jsx)(i(),{href:`/${a.slug}`,className:"block h-full",children:(0,d.jsx)("div",{className:"flex justify-center items-center h-full px-2 py-2 hover:bg-red-700",children:(0,d.jsx)("span",{className:"whitespace-nowrap uppercase text-white text-sm font-medium",children:a.text})})})},a.id))]}):null;return(0,d.jsx)("div",{id:"nav",className:`border-t border-red-800 bottom-nav ${b?"fixed top-0 shadow-md z-50 w-full":""}`,style:{backgroundColor:"#e4393c"},children:(0,d.jsxs)("div",{className:"container flex flex-nowrap items-center justify-between mx-auto px-0 md:px-2 overflow-x-scroll overflow-y-hidden md:overflow-visible",style:{scrollbarWidth:"none",msOverflowStyle:"none"},children:[(0,d.jsx)("nav",{className:"w-full md:flex md:w-auto md:order-0",children:h(0)}),(0,d.jsx)("span",{className:"md:block hidden",children:(0,d.jsx)(n,{})})]})})},p=()=>{let{menus:a}=(0,j.i)();if(!a||!Array.isArray(a))return null;let b=a.find(a=>"0"===a.position);if(!b||!b.obj)return null;let c=b.obj.reduce((a,b)=>(a[b.parent]=a[b.parent]||[],a[b.parent].push(b),a),{}),e=()=>{let a=document.getElementById("my-drawer");a&&(a.checked=!1)},f=a=>c[a]?(0,d.jsx)("ul",{children:c[a].map(a=>{let b=!!c[a.id];return(0,d.jsx)("li",{children:b?(0,d.jsxs)("details",{children:[(0,d.jsx)("summary",{children:a.text}),f(a.id)]}):(0,d.jsx)(i(),{href:`/${a.slug}`,onClick:e,children:a.text})},a.id)})}):null;return(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)("ul",{className:"menu bg-base-200 rounded-box w-56",children:f(0)})})};var q=c(89869),r=c(98166);let s=()=>{let{user:a}=(0,q.U)(),b=(0,e.usePathname)(),[c,h]=(0,f.useState)(!1);(0,f.useEffect)(()=>{document.body.style.position="relative",document.body.style.overflowY="auto";let a=()=>{h(window.innerWidth<768)};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[b]);let i=b.startsWith("/dashboard");return(0,d.jsx)(d.Fragment,{children:!i&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("header",{className:"w-full hidden md:block",style:{height:"130px"},children:(0,d.jsx)("div",{style:{width:"100%",height:"130px",position:"relative",backgroundImage:"-webkit-gradient(linear, 0% 100%, 0% 0%, color-stop(0.2, rgb(255, 255, 255)), color-stop(0.8, rgb(255, 251, 213)))"},children:(0,d.jsxs)("div",{className:"container mx-auto relative",style:{background:"url(/trongdong_1546498623413.png) no-repeat",height:"130px",display:"flex",alignItems:"center"},children:[(0,d.jsxs)("div",{className:"flex items-center ml-4",children:[(0,d.jsx)("div",{children:(0,d.jsx)("a",{href:"/",children:(0,d.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",style:{width:"90px",height:"97px"}})})}),(0,d.jsxs)("div",{className:"flex flex-col ml-3",children:[(0,d.jsx)("div",{className:"text-base md:text-lg text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,d.jsx)("div",{className:"text-red-600",style:{fontFamily:'"Roboto Condensed", sans-serif',fontSize:"44px",fontWeight:700,fontStyle:"normal",lineHeight:"48.4px",color:"#dc2626 !important",WebkitTextFillColor:"#dc2626 !important"},children:"T\xd2A \xc1N NH\xc2N D\xc2N TP.HỒ CH\xcd MINH"})]})]}),(0,d.jsx)("div",{className:"absolute right-0 top-0 h-full",children:(0,d.jsx)("img",{src:"/bg-header_1546498587110.png",alt:"H\xecnh ảnh T\xf2a \xe1n",style:{height:"130px"}})})]})})}),(0,d.jsxs)("nav",{id:"top-nav",className:"w-full z-20 border-b border-gray-200 dark:border-gray-600",children:[(0,d.jsxs)("div",{className:"container flex flex-wrap items-center justify-between mx-auto",children:[(0,d.jsx)("div",{className:"block md:hidden",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{children:(0,d.jsx)("a",{href:"/",children:(0,d.jsx)("img",{src:"/favicon.ico",alt:"Quốc huy",style:{width:"60px",height:"65px"}})})}),(0,d.jsxs)("div",{className:"flex flex-col ml-2",children:[(0,d.jsx)("div",{className:"text-xs text-blue-400 font-medium",children:"HỆ THỐNG QUẢN L\xdd HỒ SƠ GI\xc1M ĐỐC THẨM, T\xc1I THẨM"}),(0,d.jsx)("div",{className:"text-sm font-bold text-red-600",style:{color:"#dc2626 !important",WebkitTextFillColor:"#dc2626 !important"},children:"T\xd2A \xc1N NH\xc2N D\xc2N TH\xc0NH PHỐ HỒ CH\xcd MINH"})]})]})}),(0,d.jsx)("div",{className:"w-1/3 hidden md:block"}),(0,d.jsxs)("div",{className:"right-block flex menu-search items-center justify-end w-auto ml-auto",children:[a&&(0,d.jsx)("span",{className:"md:hidden block",children:(0,d.jsx)(r.A,{})}),(0,d.jsx)("div",{className:"flex md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse",children:(0,d.jsxs)("div",{className:"drawer ml-2 block md:hidden",children:[(0,d.jsx)("input",{id:"my-drawer",type:"checkbox",className:"drawer-toggle"}),(0,d.jsx)("div",{className:"drawer-content",children:(0,d.jsx)("label",{htmlFor:"my-drawer",className:"cursor-pointer"})}),(0,d.jsxs)("div",{className:"drawer-side z-50 overflow-y-auto",children:[(0,d.jsx)("label",{htmlFor:"my-drawer","aria-label":"close sidebar",className:"drawer-overlay"}),(0,d.jsxs)("div",{className:"menu bg-base-200 text-base-content min-h-full md:w-2/3 lg:w-1/2 w-full p-4",children:[(0,d.jsx)("label",{htmlFor:"my-drawer","aria-label":"close sidebar",className:"fixed right-2 top-2 w-8 h-8 bg-white rounded-full shadow-md pointer flex items-center justify-center",children:(0,d.jsx)(g.A,{})}),(0,d.jsx)(n,{}),(0,d.jsx)(p,{})]})]})]})})]})]}),(0,d.jsx)(o,{})]})]})})}},73338:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\Security\\\\SafeStyleOverride.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\Security\\SafeStyleOverride.tsx","default")},73923:(a,b,c)=>{Promise.resolve().then(c.bind(c,96908))},74603:(a,b,c)=>{Promise.resolve().then(c.bind(c,5682))},74864:(a,b,c)=>{"use strict";c.d(b,{default:()=>e});var d=c(38301);let e=()=>((0,d.useEffect)(()=>{let a=()=>{if(document.getElementById("safe-marker-override"))return;let a=document.createElement("style");a.id="safe-marker-override",a.textContent=`
        /* Override user agent stylesheet for ::marker */
        ::marker {
          unicode-bidi: normal !important;
          font-variant-numeric: normal !important;
          text-transform: inherit !important;
          text-indent: inherit !important;
          text-align: inherit !important;
          text-align-last: inherit !important;
          content: none !important;
          display: none !important;
        }

        /* Remove list markers completely */
        ul, ol {
          list-style: none !important;
        }

        ul::marker, ol::marker, li::marker {
          content: none !important;
          display: none !important;
        }

        /* Custom list styles if needed */
        .custom-list {
          list-style: disc !important;
          padding-left: 1.5rem !important;
        }

        .custom-list-ordered {
          list-style: decimal !important;
          padding-left: 1.5rem !important;
        }
      `,document.head.appendChild(a)};a();let b=setInterval(a,5e3);return()=>{clearInterval(b);let a=document.getElementById("safe-marker-override");a&&a.remove()}},[]),null)},77843:(a,b,c)=>{Promise.resolve().then(c.bind(c,74286)),Promise.resolve().then(c.t.bind(c,43059,23)),Promise.resolve().then(c.bind(c,80974)),Promise.resolve().then(c.bind(c,89869)),Promise.resolve().then(c.bind(c,96670)),Promise.resolve().then(c.bind(c,3412)),Promise.resolve().then(c.bind(c,72649)),Promise.resolve().then(c.bind(c,74864)),Promise.resolve().then(c.bind(c,21884))},88034:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(65923);let e=d.Ik({NEXT_PUBLIC_API_ENDPOINT:d.Yj().url(),NEXT_PUBLIC_URL:d.Yj().url(),CRYPTOJS_SECRECT:d.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:process.env.CRYPTOJS_SECRECT});if(!e.success)throw console.error("Invalid environment variables:",e.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let f=e.data},89869:(a,b,c)=>{"use strict";c.d(b,{U:()=>g,default:()=>h});var d=c(21124),e=c(38301);let f=(0,e.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),g=()=>(0,e.useContext)(f),h=({children:a})=>{let[b,c]=(0,e.useState)(()=>null),[g,h]=(0,e.useState)(!0),i=(0,e.useCallback)(a=>{c(a),localStorage.setItem("user",JSON.stringify(a))},[c]);return(0,e.useEffect)(()=>{let a=localStorage.getItem("user");c(a?JSON.parse(a):null),h(!1)},[c]),(0,d.jsx)(f.Provider,{value:{user:b,setUser:i,isAuthenticated:!!b,isLoading:g},children:a})}},91492:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(97954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\components\\\\DynamicTitle.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\components\\DynamicTitle.tsx","default")},91683:(a,b,c)=>{"use strict";c.d(b,{SettingProvider:()=>e});var d=c(97954);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call SettingProvider() from the server but SettingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SettingContext.tsx","SettingProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useSetting() from the server but useSetting is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\context\\SettingContext.tsx","useSetting")},96670:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(38301),e=c(21884);let f=()=>{let{setting:a,loading:b}=(0,e.i)();return(0,d.useEffect)(()=>{if(!b&&a?.title&&(document.title=a.title,a.desc)){let b=document.querySelector('meta[name="description"]');if(b)b.setAttribute("content",a.desc);else{let b=document.createElement("meta");b.name="description",b.content=a.desc,document.head.appendChild(b)}}},[a,b]),null}},96908:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(21124);function e({error:a,reset:b}){return(0,d.jsx)("div",{className:"bg-gray-100 py-16 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-lg max-w-md w-full",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-8",children:"404 - Page Not Found"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"The page you are looking for might have been removed, had its name changed or is temporarily unavailable."}),(0,d.jsx)("a",{href:"/",className:"inline-block py-3 px-6 bg-blue-500 hover:bg-blue-600 text-white rounded-lg font-semibold",children:"Go back to homepage"})]})})}c(38301)},98166:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(21124),e=c(3991),f=c.n(e),g=c(89869),h=c(50886),i=c(5085);function j(){let{user:a,isAuthenticated:b}=(0,g.U)(),{hasPermission:c}=(0,h.A)();return(0,d.jsx)("div",{className:"flex-shrink-0 flex items-center justify-end",children:(0,d.jsxs)("div",{className:"dropdown dropdown-end",children:[(0,d.jsx)("div",{className:"tooltip tooltip-left","data-tip":a?.username,children:(0,d.jsx)("label",{tabIndex:0,className:"cursor-pointer flex w-8 h-8 md:w-10 md:h-10 bg-yellow-400 rounded-full text-center text-gray-900 items-center justify-center text-xl mb-0 uppercase",children:a?.username?.charAt(0)})}),(0,d.jsxs)("ul",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow",children:[[{title:"T\xe0i khoản",to:"/dashboard"}].map((a,b)=>(0,d.jsx)("li",{children:(0,d.jsx)(f(),{href:a.to,children:a.title})},b)),(0,d.jsx)("li",{children:(0,d.jsx)(i.A,{})})]})]})})}},99815:(a,b,c)=>{"use strict";c.d(b,{default:()=>e});var d=c(97954);(0,d.registerClientReference)(function(){throw Error("Attempted to call useAppContext() from the server but useAppContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\app-provider.tsx","useAppContext");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\blog\\\\tandpro\\\\src\\\\app\\\\app-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\blog\\tandpro\\src\\app\\app-provider.tsx","default")}};