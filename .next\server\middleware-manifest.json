{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/register(\\.json)?[\\/#\\?]?$", "originalSource": "/register"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/change-password(\\.json)?[\\/#\\?]?$", "originalSource": "/change-password"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/forgot-pass(\\.json)?[\\/#\\?]?$", "originalSource": "/forgot-pass"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "pLSrmSGtzMn7JV1pJxp1g", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oUZ3UfuXv2lEoP4mrKf3WzsbPUhYH20/y7r2/pok7Fs=", "__NEXT_PREVIEW_MODE_ID": "e34425401ba3ee8eac973233388085c5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "86122a5ee85b0cd30e4360f93a80adb1f1b30e3b330086eced907804636cb730", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "675571078a23b22ca631a2789600817c5d9646a7cd54766fa8ae22aa1a7d0e62"}}}, "functions": {}, "sortedMiddleware": ["/"]}