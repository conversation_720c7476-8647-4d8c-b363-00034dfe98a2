(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2456],{548:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(12115),l=t(12758),o=t.n(l);function a(){return(a=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}var c=(0,n.forwardRef)(function(e,r){var t=e.color,l=e.size,o=void 0===l?24:l,c=function(e,r){if(null==e)return{};var t,n,l=function(e,r){if(null==e)return{};var t,n,l={},o=Object.keys(e);for(n=0;n<o.length;n++)t=o[n],r.indexOf(t)>=0||(l[t]=e[t]);return l}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)t=o[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(l[t]=e[t])}return l}(e,["color","size"]);return n.createElement("svg",a({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===t?"currentColor":t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},c),n.createElement("circle",{cx:"11",cy:"11",r:"8"}),n.createElement("line",{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}))});c.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},c.displayName="Search";let i=c},20898:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},24033:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(71847).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},33262:()=>{},70706:e=>{e.exports={style:{fontFamily:"'Libre Baskerville', 'Libre Baskerville Fallback'"},className:"__className_f3a3b8",variable:"__variable_f3a3b8"}},71847:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var n=t(12115);let l=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:l=24,strokeWidth:c=2,absoluteStrokeWidth:i,className:s="",children:u,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:r,...a,width:l,height:l,stroke:t,strokeWidth:i?24*Number(c)/Number(l):c,className:o("lucide",s),...!u&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),i=(e,r)=>{let t=(0,n.forwardRef)((t,a)=>{let{className:i,...s}=t;return(0,n.createElement)(c,{ref:a,iconNode:r,className:o("lucide-".concat(l(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...s})});return t.displayName=l(e),t}},84604:(e,r,t)=>{"use strict";t.d(r,{GoogleOAuthProvider:()=>o});var n=t(12115);let l=(0,n.createContext)(null);function o(e){let{clientId:r,nonce:t,onScriptLoadSuccess:o,onScriptLoadError:a,children:c}=e,i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{nonce:r,onScriptLoadSuccess:t,onScriptLoadError:l}=e,[o,a]=(0,n.useState)(!1),c=(0,n.useRef)(t);c.current=t;let i=(0,n.useRef)(l);return i.current=l,(0,n.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=r,e.onload=()=>{var e;a(!0),null==(e=c.current)||e.call(c)},e.onerror=()=>{var e;a(!1),null==(e=i.current)||e.call(i)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[r]),o}({nonce:t,onScriptLoadSuccess:o,onScriptLoadError:a}),s=(0,n.useMemo)(()=>({clientId:r,scriptLoadedSuccessfully:i}),[r,i]);return n.createElement(l.Provider,{value:s},c)}}}]);