"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3078],{25656:(e,t,r)=>{r.d(t,{Ay:()=>c});var a=r(79902),s=r(64269),i=r(20063);class n extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class o extends n{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,d=async(e,t,r)=>{let d;(null==r?void 0:r.body)instanceof FormData?d=r.body:(null==r?void 0:r.body)&&(d=JSON.stringify(r.body));let c=d instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(c.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,m=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),p=await fetch(m,{...r,headers:{...c,...null==r?void 0:r.headers},body:d,method:e}),h=null,f=p.headers.get("content-type");if(f&&f.includes("application/json"))try{h=await p.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await p.text();let g={status:p.status,payload:h};if(!p.ok)if(404===p.status||403===p.status)throw new o(g);else if(401===p.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,i.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...c}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new n(g);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return g},c={get:(e,t)=>d("GET",e,t),post:(e,t,r)=>d("POST",e,{...r,body:t}),put:(e,t,r)=>d("PUT",e,{...r,body:t}),patch:(e,t,r)=>d("PATCH",e,{...r,body:t}),delete:(e,t)=>d("DELETE",e,{...t})}},41502:(e,t,r)=>{r.d(t,{PD:()=>i,aP:()=>n,gS:()=>o});var a=r(77376);let s=a.Ik({_id:a.Yj(),username:a.Yj(),phonenumber:a.ai(),email:a.Yj().email(),createdAt:a.Yj().date(),private:a.zM(),rule:a.Yj()});a.Ik({total:a.ai(),users:a.YO(s)});let i=a.Ik({username:a.Yj().trim().min(2).max(256),email:a.Yj().email(),password:a.Yj().min(6).max(100),phonenumber:a.Yj(),department:a.Yj().optional(),permissions:a.YO(a.Yj()).optional()}).strict(),n=a.Ik({_id:a.Yj(),username:a.Yj().trim().min(2).max(256),email:a.Yj().email(),phonenumber:a.bz(),private:a.zM(),rule:a.k5(["user","admin","manager","editor"]).optional(),rank:a.Yj().optional(),gender:a.k5(["Male","Female","Not"]),bio:a.bz(),permissions:a.YO(a.Yj())}),o=a.Ik({_id:a.Yj(),password:a.Yj().min(6,"Mật khẩu phải c\xf3 \xedt nhất 6 k\xfd tự"),confirmPassword:a.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Mật khẩu x\xe1c nhận kh\xf4ng khớp",path:["confirmPassword"]}),l=a.Ik({_id:a.Yj(),user:a.Yj(),ip:a.Yj(),device:a.Yj(),loginTime:a.Yj().datetime(),logoutTime:a.Yj().datetime().nullable()});a.Ik({logs:a.YO(l)})},43281:(e,t,r)=>{r.d(t,{lV:()=>u,MJ:()=>y,zB:()=>p,eI:()=>g,lR:()=>v,C5:()=>j});var a=r(95155),s=r(12115),i=r(46673),n=r(22544),o=r(64269),l=r(46554);let d=(0,r(83101).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,o.cn)(d(),r),...s})});c.displayName=l.b.displayName;let u=n.Op,m=s.createContext({}),p=e=>{let{...t}=e;return(0,a.jsx)(m.Provider,{value:{name:t.name},children:(0,a.jsx)(n.xI,{...t})})},h=()=>{let e=s.useContext(m),t=s.useContext(f),{getFieldState:r,formState:a}=(0,n.xW)(),i=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},f=s.createContext({}),g=s.forwardRef((e,t)=>{let{className:r,...i}=e,n=s.useId();return(0,a.jsx)(f.Provider,{value:{id:n},children:(0,a.jsx)("div",{ref:t,className:(0,o.cn)("mb-4",r),...i})})});g.displayName="FormItem";let v=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:i,formItemId:n}=h();return(0,a.jsx)(c,{ref:t,className:(0,o.cn)(i&&"text-destructive",r),htmlFor:n,...s})});v.displayName="FormLabel";let y=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:n,formDescriptionId:o,formMessageId:l}=h();return(0,a.jsx)(i.DX,{ref:t,id:n,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...r})});y.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:i}=h();return(0,a.jsx)("p",{ref:t,id:i,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",r),...s})}).displayName="FormDescription";let j=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e,{error:n,formMessageId:l}=h(),d=n?String(null==n?void 0:n.message):s;return d?(0,a.jsx)("p",{ref:t,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-red-600",r),...i,children:d}):null});j.displayName="FormMessage"},48203:(e,t,r)=>{r.d(t,{default:()=>o});var a=r(95155),s=r(75444),i=r(20063),n=r(12115);function o(e){let{children:t,requiredPermission:r,requiredPermissions:o=[],requireAll:l=!1,fallbackPath:d="/dashboard"}=e,{hasPermission:c,hasAnyPermission:u,isAdmin:m,isDepartmentManager:p,isLoading:h}=(0,s.S)(),f=(0,i.useRouter)();if((0,n.useEffect)(()=>{if(!h&&!m)(r?"admin"===r&&!!p||c(r):!(o.length>0)||(l?o.every(e=>c(e)):u(o)))||f.replace(d)},[c,u,m,p,h,r,o,l,d,f]),h)return(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(m)return(0,a.jsx)(a.Fragment,{children:t});return(r?"admin"===r&&!!p||c(r):!(o.length>0)||(l?o.every(e=>c(e)):u(o)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,a.jsx)("button",{onClick:()=>f.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},64269:(e,t,r)=>{r.d(t,{Fd:()=>n,cn:()=>i}),r(25656);var a=r(2821),s=r(75889);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}r(138);let n=e=>e.startsWith("/")?e.slice(1):e},65142:(e,t,r)=>{r.d(t,{p:()=>l});var a=r(95155),s=r(64269),i=r(88109),n=r(67553),o=r(12115);let l=o.forwardRef((e,t)=>{let{className:r,type:l,...d}=e,[c,u]=(0,o.useState)(!1);return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsx)("input",{type:"password"===l&&c?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,s.cn)("input input-bordered w-full rounded-md",r),ref:t,...d}),"password"===l&&(c?(0,a.jsx)(i.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!c)}):(0,a.jsx)(n.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!c)}))]})})});l.displayName="Input"},75444:(e,t,r)=>{r.d(t,{S:()=>s});var a=r(97367);let s=()=>{let{user:e,isLoading:t}=(0,a.U)();return{hasPermission:r=>{var a;return!t&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},78296:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(25656);let s={fetchUsers:(e,t)=>a.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>a.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>a.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>a.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>a.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>a.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>a.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>a.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},79902:(e,t,r)=>{r.d(t,{A:()=>n});var a=r(77376),s=r(95704);let i=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!i.success)throw console.error("Invalid environment variables:",i.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let n=i.data},97367:(e,t,r)=>{r.d(t,{U:()=>n,default:()=>o});var a=r(95155),s=r(12115);let i=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),n=()=>(0,s.useContext)(i),o=e=>{let{children:t}=e,[r,n]=(0,s.useState)(()=>null),[o,l]=(0,s.useState)(!0),d=(0,s.useCallback)(e=>{n(e),localStorage.setItem("user",JSON.stringify(e))},[n]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");n(e?JSON.parse(e):null),l(!1)},[n]),(0,a.jsx)(i.Provider,{value:{user:r,setUser:d,isAuthenticated:!!r,isLoading:o},children:t})}}}]);