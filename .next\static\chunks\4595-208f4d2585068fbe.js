"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4595],{21786:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(71847).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},25656:(e,t,r)=>{r.d(t,{Ay:()=>u});var a=r(79902),s=r(64269),n=r(20063);class l extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class o extends l{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let i=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,h=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),m=await fetch(h,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),p=null,g=m.headers.get("content-type");if(g&&g.includes("application/json"))try{p=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await m.text();let f={status:m.status,payload:p};if(!m.ok)if(404===m.status||403===m.status)throw new o(f);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!i){i=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await i}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),i=null,location.href="/login"}}}else throw new l(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},48203:(e,t,r)=>{r.d(t,{default:()=>o});var a=r(95155),s=r(75444),n=r(20063),l=r(12115);function o(e){let{children:t,requiredPermission:r,requiredPermissions:o=[],requireAll:i=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:u,hasAnyPermission:d,isAdmin:h,isDepartmentManager:m,isLoading:p}=(0,s.S)(),g=(0,n.useRouter)();if((0,l.useEffect)(()=>{if(!p&&!h)(r?"admin"===r&&!!m||u(r):!(o.length>0)||(i?o.every(e=>u(e)):d(o)))||g.replace(c)},[u,d,h,m,p,r,o,i,c,g]),p)return(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,a.jsx)(a.Fragment,{children:t});return(r?"admin"===r&&!!m||u(r):!(o.length>0)||(i?o.every(e=>u(e)):d(o)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,a.jsx)("button",{onClick:()=>g.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},64269:(e,t,r)=>{r.d(t,{Fd:()=>l,cn:()=>n}),r(25656);var a=r(2821),s=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}r(138);let l=e=>e.startsWith("/")?e.slice(1):e},71847:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(12115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:c="",children:u,iconNode:d,...h}=e;return(0,a.createElement)("svg",{ref:t,...l,width:s,height:s,stroke:r,strokeWidth:i?24*Number(o)/Number(s):o,className:n("lucide",c),...!u&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(h)&&{"aria-hidden":"true"},...h},[...d.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),i=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:i,...c}=r;return(0,a.createElement)(o,{ref:l,iconNode:t,className:n("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),i),...c})});return r.displayName=s(e),r}},75444:(e,t,r)=>{r.d(t,{S:()=>s});var a=r(97367);let s=()=>{let{user:e,isLoading:t}=(0,a.U)();return{hasPermission:r=>{var a;return!t&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},79902:(e,t,r)=>{r.d(t,{A:()=>l});var a=r(77376),s=r(95704);let n=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let l=n.data},97367:(e,t,r)=>{r.d(t,{U:()=>l,default:()=>o});var a=r(95155),s=r(12115);let n=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),l=()=>(0,s.useContext)(n),o=e=>{let{children:t}=e,[r,l]=(0,s.useState)(()=>null),[o,i]=(0,s.useState)(!0),c=(0,s.useCallback)(e=>{l(e),localStorage.setItem("user",JSON.stringify(e))},[l]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");l(e?JSON.parse(e):null),i(!1)},[l]),(0,a.jsx)(n.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:o},children:t})}},97849:(e,t,r)=>{r.d(t,{Yq:()=>s,ZV:()=>n,z3:()=>a});let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(t<0?0:t))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][r]},s=e=>{let t;if(!e)return"";if("string"==typeof e){if(e.match(/^\d{2}\/\d{2}\/\d{4}$/))return e;if(e.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[r,a,s]=e.split("/");t=new Date(parseInt(s),parseInt(a)-1,parseInt(r))}else t=new Date(e)}else t=new Date(e);if(isNaN(t.getTime()))return"";let r=t.getDate().toString().padStart(2,"0"),a=(t.getMonth()+1).toString().padStart(2,"0"),s=t.getFullYear().toString();return"".concat(r,"/").concat(a,"/").concat(s)},n=e=>e.toLocaleString()}}]);