(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5006],{25656:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d});var r=s(79902),a=s(64269),o=s(20063);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class n extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,s)=>{let c;(null==s?void 0:s.body)instanceof FormData?c=s.body:(null==s?void 0:s.body)&&(c=JSON.stringify(s.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==s?void 0:s.baseUrl)===void 0?r.A.NEXT_PUBLIC_API_ENDPOINT:s.baseUrl,m=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),p=await fetch(m,{...s,headers:{...d,...null==s?void 0:s.headers},body:c,method:e}),y=null,h=p.headers.get("content-type");if(h&&h.includes("application/json"))try{y=await p.json()}catch(e){console.error("Failed to parse JSON response:",e),y=null}else y=await p.text();let f={status:p.status,payload:y};if(!p.ok)if(404===p.status||403===p.status)throw new n(f);else if(401===p.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,o.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new i(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=y;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},d={get:(e,t)=>c("GET",e,t),post:(e,t,s)=>c("POST",e,{...s,body:t}),put:(e,t,s)=>c("PUT",e,{...s,body:t}),patch:(e,t,s)=>c("PATCH",e,{...s,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},36317:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(25656);s(40618).config();let a={login:e=>r.Ay.post("/api/auth/login",e),register:e=>r.Ay.post("/api/auth/signup",e),forgot:e=>r.Ay.put("/api/auth/app-forgot-pass",e),changepass:e=>r.Ay.put("/api/auth/app-reset-pass",e),auth:e=>r.Ay.post("/api/auth",e,{baseUrl:""}),checkCode:e=>r.Ay.get("/api/auth/check-code/".concat(e)),VerifyAppCode:e=>r.Ay.post("/api/auth/verify-app-code",e),VerifyCode:e=>r.Ay.post("/api/auth/verify-code",e),logoutFromNextServerToServer:e=>r.Ay.post("/api/auth/blacklist-token/",e,{headers:{Authorization:"Bearer ".concat(e.sessionToken)}}),logoutFromNextClientToNextServer:(e,t)=>r.Ay.post("/api/auth/logout",{force:e},{baseUrl:"",signal:t}),slideSessionFromNextServerToServer:e=>r.Ay.post("/auth/slide-session",{},{headers:{Authorization:"Bearer ".concat(e)}}),slideSessionFromNextClientToNextServer:()=>r.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},41759:(e,t,s)=>{"use strict";s.d(t,{A:()=>f});var r=s(95155),a=s(12115),o=s(20063),i=s(77873),n=s(22544),l=s(33602),c=s(43281),d=s(65142),u=s(36317),m=s(95113),p=s(56599),y=s(74744),h=s(97367);function f(e){let{userId:t,typeVerify:s}=e,[f,g]=(0,a.useState)(null),[A,x]=(0,a.useState)(!1),v=(0,o.useRouter)(),{setUser:b}=(0,h.U)(),N=(0,n.mN)({resolver:(0,l.u)(m.ZZ),defaultValues:{code:"",userId:t,deviceId:""}});async function w(e){if(A)return;x(!0);let t=await (0,i.I)();try{let r=null;"authapp"===s?r=await u.A.VerifyAppCode({...e,deviceId:t}):"authmail"===s&&(r=await u.A.VerifyCode({...e,deviceId:t})),r&&(await u.A.auth({sessionToken:r.payload.token,expiresAt:r.payload.expiresAt,user:r.payload.user}),y.oR.success("Đăng nhập th\xe0nh c\xf4ng!"),b(r.payload.user),v.push("/dashboard"),v.refresh())}catch(e){403===e.status&&g(e.payload.code),y.oR.error(e.payload.message)}finally{x(!1)}}return(0,r.jsx)(c.lV,{...N,children:(0,r.jsxs)("form",{className:"mx-auto w-full mb-5",onSubmit:N.handleSubmit(w),noValidate:!0,children:[(0,r.jsx)(c.zB,{control:N.control,name:"code",render:e=>{let{field:t}=e;return(0,r.jsxs)(c.eI,{children:[(0,r.jsx)(c.MJ,{children:(0,r.jsx)(d.p,{placeholder:"Code",type:"text",...t})}),(0,r.jsx)(c.C5,{})]})}}),(0,r.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:f}),(0,r.jsxs)("button",{disabled:!!A,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:[A?(0,r.jsx)(p.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})]})})}},43281:(e,t,s)=>{"use strict";s.d(t,{lV:()=>u,MJ:()=>A,zB:()=>p,eI:()=>f,lR:()=>g,C5:()=>x});var r=s(95155),a=s(12115),o=s(46673),i=s(22544),n=s(64269),l=s(46554);let c=(0,s(83101).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)(c(),s),...a})});d.displayName=l.b.displayName;let u=i.Op,m=a.createContext({}),p=e=>{let{...t}=e;return(0,r.jsx)(m.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},y=()=>{let e=a.useContext(m),t=a.useContext(h),{getFieldState:s,formState:r}=(0,i.xW)(),o=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...o}},h=a.createContext({}),f=a.forwardRef((e,t)=>{let{className:s,...o}=e,i=a.useId();return(0,r.jsx)(h.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,n.cn)("mb-4",s),...o})})});f.displayName="FormItem";let g=a.forwardRef((e,t)=>{let{className:s,...a}=e,{error:o,formItemId:i}=y();return(0,r.jsx)(d,{ref:t,className:(0,n.cn)(o&&"text-destructive",s),htmlFor:i,...a})});g.displayName="FormLabel";let A=a.forwardRef((e,t)=>{let{...s}=e,{error:a,formItemId:i,formDescriptionId:n,formMessageId:l}=y();return(0,r.jsx)(o.DX,{ref:t,id:i,"aria-describedby":a?"".concat(n," ").concat(l):"".concat(n),"aria-invalid":!!a,...s})});A.displayName="FormControl",a.forwardRef((e,t)=>{let{className:s,...a}=e,{formDescriptionId:o}=y();return(0,r.jsx)("p",{ref:t,id:o,className:(0,n.cn)("text-[0.8rem] text-muted-foreground",s),...a})}).displayName="FormDescription";let x=a.forwardRef((e,t)=>{let{className:s,children:a,...o}=e,{error:i,formMessageId:l}=y(),c=i?String(null==i?void 0:i.message):a;return c?(0,r.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-[0.8rem] font-medium text-red-600",s),...o,children:c}):null});x.displayName="FormMessage"},64269:(e,t,s)=>{"use strict";s.d(t,{Fd:()=>i,cn:()=>o}),s(25656);var r=s(2821),a=s(75889);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}s(138);let i=e=>e.startsWith("/")?e.slice(1):e},65142:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(95155),a=s(64269),o=s(88109),i=s(67553),n=s(12115);let l=n.forwardRef((e,t)=>{let{className:s,type:l,...c}=e,[d,u]=(0,n.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)("input",{type:"password"===l&&d?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,a.cn)("input input-bordered w-full rounded-md",s),ref:t,...c}),"password"===l&&(d?(0,r.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}):(0,r.jsx)(i.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}))]})})});l.displayName="Input"},72016:()=>{},77873:(e,t,s)=>{"use strict";s.d(t,{I:()=>a});var r=s(23997);let a=async()=>{let e=await r.Ay.load();return(await e.get()).visitorId}},79902:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(77376),a=s(95704);let o=r.Ik({NEXT_PUBLIC_API_ENDPOINT:r.Yj().url(),NEXT_PUBLIC_URL:r.Yj().url(),CRYPTOJS_SECRECT:r.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!o.success)throw console.error("Invalid environment variables:",o.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=o.data},95113:(e,t,s)=>{"use strict";s.d(t,{Ap:()=>n,ZZ:()=>l,aU:()=>a,ab:()=>i,iV:()=>o});var r=s(37424);let a=r.Ay.object({username:r.Ay.string().trim().min(2).max(256),email:r.Ay.string().email(),password:r.Ay.string().min(6).max(100),confirmPassword:r.Ay.string().min(6).max(100)}).strict().superRefine((e,t)=>{let{confirmPassword:s,password:r}=e;s!==r&&t.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})});r.Ay.object({token:r.Ay.string(),user:r.Ay.object({_id:r.Ay.number(),username:r.Ay.string(),email:r.Ay.string(),rule:r.Ay.string()}),message:r.Ay.string()});let o=r.Ay.object({email:r.Ay.string().email(),password:r.Ay.string().min(6).max(100),deviceId:r.Ay.string()}).strict(),i=r.Ay.object({email:r.Ay.string().email()}).strict(),n=r.Ay.object({email:r.Ay.string().email(),code:r.Ay.string(),password:r.Ay.string().min(6).max(100)}).strict();r.Ay.object({}).strict();let l=r.Ay.object({code:r.Ay.string().min(6),userId:r.Ay.string(),deviceId:r.Ay.string()}).strict();r.Ay.object({userId:r.Ay.string()}).strict()},97367:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,default:()=>n});var r=s(95155),a=s(12115);let o=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,a.useContext)(o),n=e=>{let{children:t}=e,[s,i]=(0,a.useState)(()=>null),[n,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),l(!1)},[i]),(0,r.jsx)(o.Provider,{value:{user:s,setUser:c,isAuthenticated:!!s,isLoading:n},children:t})}}}]);