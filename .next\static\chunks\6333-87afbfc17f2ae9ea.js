"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6333],{25656:(e,t,a)=>{a.d(t,{Ay:()=>d});var r=a(79902),s=a(64269),n=a(20063);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class l extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let o=null,c=async(e,t,a)=>{let c;(null==a?void 0:a.body)instanceof FormData?c=a.body:(null==a?void 0:a.body)&&(c=JSON.stringify(a.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==a?void 0:a.baseUrl)===void 0?r.A.NEXT_PUBLIC_API_ENDPOINT:a.baseUrl,h=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),m=await fetch(h,{...a,headers:{...d,...null==a?void 0:a.headers},body:c,method:e}),g=null,p=m.headers.get("content-type");if(p&&p.includes("application/json"))try{g=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),g=null}else g=await m.text();let y={status:m.status,payload:g};if(!m.ok)if(404===m.status||403===m.status)throw new l(y);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!o){o=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await o}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),o=null,location.href="/login"}}}else throw new i(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=g;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},d={get:(e,t)=>c("GET",e,t),post:(e,t,a)=>c("POST",e,{...a,body:t}),put:(e,t,a)=>c("PUT",e,{...a,body:t}),patch:(e,t,a)=>c("PATCH",e,{...a,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},42080:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(95155);function s(e){let{currentPage:t,totalPages:a,onPageChange:s}=e;return(0,r.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,r.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>s(Math.max(t-1,1)),disabled:1===t,children:"Previous"}),(0,r.jsxs)("span",{children:["Page ",t," / ",a]}),(0,r.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>s(Math.min(t+1,a)),disabled:t===a,children:"Next"})]})}a(12115)},48203:(e,t,a)=>{a.d(t,{default:()=>l});var r=a(95155),s=a(75444),n=a(20063),i=a(12115);function l(e){let{children:t,requiredPermission:a,requiredPermissions:l=[],requireAll:o=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:h,isDepartmentManager:m,isLoading:g}=(0,s.S)(),p=(0,n.useRouter)();if((0,i.useEffect)(()=>{if(!g&&!h)(a?"admin"===a&&!!m||d(a):!(l.length>0)||(o?l.every(e=>d(e)):u(l)))||p.replace(c)},[d,u,h,m,g,a,l,o,c,p]),g)return(0,r.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,r.jsx)(r.Fragment,{children:t});return(a?"admin"===a&&!!m||d(a):!(l.length>0)||(o?l.every(e=>d(e)):u(l)))?(0,r.jsx)(r.Fragment,{children:t}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,r.jsx)("button",{onClick:()=>p.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},52799:(e,t,a)=>{a.d(t,{Ex:()=>s,eG:()=>n});var r=a(95155);a(12115);let s=e=>{let{children:t,variant:a="default",size:s="md",className:n="",dot:i=!1}=e;return(0,r.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[a],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[s],"\n        ").concat(n,"\n      "),children:[i&&(0,r.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[a])}),t]})},n=e=>{let{role:t,className:a=""}=e,n={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},i=n[t]||n.user;return(0,r.jsx)(s,{variant:i.variant,className:a,children:i.label})}},64269:(e,t,a)=>{a.d(t,{Fd:()=>i,cn:()=>n}),a(25656);var r=a(2821),s=a(75889);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}a(138);let i=e=>e.startsWith("/")?e.slice(1):e},75444:(e,t,a)=>{a.d(t,{S:()=>s});var r=a(97367);let s=()=>{let{user:e,isLoading:t}=(0,r.U)();return{hasPermission:a=>{var r;return!t&&!!e&&("admin"===e.rule||(null==(r=e.permissions)?void 0:r.includes(a))||!1)},hasAnyPermission:a=>!t&&!!e&&("admin"===e.rule||a.some(t=>{var a;return null==(a=e.permissions)?void 0:a.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},78296:(e,t,a)=>{a.d(t,{A:()=>s});var r=a(25656);let s={fetchUsers:(e,t)=>r.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),getAllUsers:(e,t)=>r.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>r.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>r.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,a)=>r.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:a}),CreateUser:(e,t)=>r.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>r.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>r.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},79902:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(77376),s=a(95704);let n=r.Ik({NEXT_PUBLIC_API_ENDPOINT:r.Yj().url(),NEXT_PUBLIC_URL:r.Yj().url(),CRYPTOJS_SECRECT:r.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=n.data},97367:(e,t,a)=>{a.d(t,{U:()=>i,default:()=>l});var r=a(95155),s=a(12115);let n=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,s.useContext)(n),l=e=>{let{children:t}=e,[a,i]=(0,s.useState)(()=>null),[l,o]=(0,s.useState)(!0),c=(0,s.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),o(!1)},[i]),(0,r.jsx)(n.Provider,{value:{user:a,setUser:c,isAuthenticated:!!a,isLoading:l},children:t})}}}]);