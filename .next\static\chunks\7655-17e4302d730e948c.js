(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7655],{12213:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(25656);let a={createDepartment:(e,t)=>s.Ay.post("/api/departments",e,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartments:(e,t)=>s.Ay.post("/api/departments/list",e,{headers:{Authorization:"Bearer ".concat(t)}}),getDepartmentById:(e,t)=>s.Ay.get("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),updateDepartment:(e,t,r)=>s.Ay.put("/api/departments/".concat(e),t,{headers:{Authorization:"Bearer ".concat(r)}}),deleteDepartment:(e,t)=>s.Ay.delete("/api/departments/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),addMemberToDepartment:(e,t,r)=>s.Ay.post("/api/departments/".concat(e,"/members"),t,{headers:{Authorization:"Bearer ".concat(r)}}),getDepartmentMembers:(e,t,r)=>s.Ay.post("/api/departments/".concat(e,"/members/list"),t,{headers:{Authorization:"Bearer ".concat(r)}}),updateMemberPermissions:(e,t,r,a)=>s.Ay.put("/api/departments/".concat(e,"/members/").concat(t),r,{headers:{Authorization:"Bearer ".concat(a)}}),removeMemberFromDepartment:(e,t,r)=>s.Ay.delete("/api/departments/".concat(e,"/members/").concat(t),{headers:{Authorization:"Bearer ".concat(r)}}),getAvailablePermissions:e=>s.Ay.get("/api/departments/permissions",{headers:{Authorization:"Bearer ".concat(e)}})}},12758:(e,t,r)=>{e.exports=r(19298)()},19298:(e,t,r)=>{"use strict";var s=r(53341);function a(){}function n(){}n.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,n,o){if(o!==s){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:n,resetWarningCache:a};return r.PropTypes=r,r}},25656:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var s=r(79902),a=r(64269),n=r(20063);class o extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends o{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?s.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,p=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),m=await fetch(p,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),h=null,y=m.headers.get("content-type");if(y&&y.includes("application/json"))try{h=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await m.text();let f={status:m.status,payload:h};if(!m.ok)if(404===m.status||403===m.status)throw new i(f);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new o(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},48203:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(95155),a=r(75444),n=r(20063),o=r(12115);function i(e){let{children:t,requiredPermission:r,requiredPermissions:i=[],requireAll:l=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:u,hasAnyPermission:d,isAdmin:p,isDepartmentManager:m,isLoading:h}=(0,a.S)(),y=(0,n.useRouter)();if((0,o.useEffect)(()=>{if(!h&&!p)(r?"admin"===r&&!!m||u(r):!(i.length>0)||(l?i.every(e=>u(e)):d(i)))||y.replace(c)},[u,d,p,m,h,r,i,l,c,y]),h)return(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(p)return(0,s.jsx)(s.Fragment,{children:t});return(r?"admin"===r&&!!m||u(r):!(i.length>0)||(l?i.every(e=>u(e)):d(i)))?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,s.jsx)("button",{onClick:()=>y.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},53341:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},64269:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>o,cn:()=>n}),r(25656);var s=r(2821),a=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}r(138);let o=e=>e.startsWith("/")?e.slice(1):e},75444:(e,t,r)=>{"use strict";r.d(t,{S:()=>a});var s=r(97367);let a=()=>{let{user:e,isLoading:t}=(0,s.U)();return{hasPermission:r=>{var s;return!t&&!!e&&("admin"===e.rule||(null==(s=e.permissions)?void 0:s.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},79902:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(77376),a=r(95704);let n=s.Ik({NEXT_PUBLIC_API_ENDPOINT:s.Yj().url(),NEXT_PUBLIC_URL:s.Yj().url(),CRYPTOJS_SECRECT:s.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let o=n.data},95761:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(12115),a=r(12758),n=r.n(a);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}var i=(0,s.forwardRef)(function(e,t){var r=e.color,a=e.size,n=void 0===a?24:a,i=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r,s,a={},n=Object.keys(e);for(s=0;s<n.length;s++)r=n[s],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,["color","size"]);return s.createElement("svg",o({ref:t,xmlns:"http://www.w3.org/2000/svg",width:n,height:n,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("line",{x1:"19",y1:"12",x2:"5",y2:"12"}),s.createElement("polyline",{points:"12 19 5 12 12 5"}))});i.propTypes={color:n().string,size:n().oneOfType([n().string,n().number])},i.displayName="ArrowLeft";let l=i},97367:(e,t,r)=>{"use strict";r.d(t,{U:()=>o,default:()=>i});var s=r(95155),a=r(12115);let n=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),o=()=>(0,a.useContext)(n),i=e=>{let{children:t}=e,[r,o]=(0,a.useState)(()=>null),[i,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{o(e),localStorage.setItem("user",JSON.stringify(e))},[o]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");o(e?JSON.parse(e):null),l(!1)},[o]),(0,s.jsx)(n.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}}}]);