(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2766],{44168:(e,a,t)=>{Promise.resolve().then(t.bind(t,99105))},99105:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var n=t(95155),c=t(12115),s=t(20063),l=t(41759),r=t(36317);function i(){return(0,n.jsx)(c.Suspense,{fallback:(0,n.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,n.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:"Loading..."})}),children:(0,n.jsx)(d,{})})}function d(){let e=(0,s.useSearchParams)().get("id")||"",[a,t]=(0,c.useState)(""),[i,d]=(0,c.useState)("");return((0,c.useEffect)(()=>{if(!e)return void d("Invalid verification code.");(async()=>{try{var a,n;let c=await r.A.checkCode(e),s=null!=(n=null==c||null==(a=c.payload)?void 0:a.userId)?n:"";t(s)}catch(e){d("Failed to load verification. Please try again later.")}})()},[e]),i)?(0,n.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,n.jsx)("p",{children:i})}):a?(0,n.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,n.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,n.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,n.jsx)("h1",{className:"text-2xl text-center mb-4",children:"X\xe1c nhận bước 2"}),(0,n.jsx)("span",{className:"text-center block mb-4",children:"Mở ứng dụng x\xe1c thực hai yếu tố tr\xean thiết bị của bạn để xem m\xe3 x\xe1c thực v\xe0 x\xe1c minh danh t\xednh của bạn"}),(0,n.jsx)(l.A,{userId:a,typeVerify:"authapp"})]})})}):(0,n.jsx)("div",{className:"container mx-auto py-4 px-4",children:(0,n.jsx)("p",{children:"Loading user information..."})})}}},e=>{e.O(0,[9268,2739,4744,7325,5885,5006,8441,1255,7358],()=>e(e.s=44168)),_N_E=e.O()}]);