(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4807],{25033:(e,t,s)=>{Promise.resolve().then(s.bind(s,81560))},25656:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d});var r=s(79902),a=s(64269),o=s(20063);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class n extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,s)=>{let c;(null==s?void 0:s.body)instanceof FormData?c=s.body:(null==s?void 0:s.body)&&(c=JSON.stringify(s.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==s?void 0:s.baseUrl)===void 0?r.A.NEXT_PUBLIC_API_ENDPOINT:s.baseUrl,m=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),p=await fetch(m,{...s,headers:{...d,...null==s?void 0:s.headers},body:c,method:e}),h=null,f=p.headers.get("content-type");if(f&&f.includes("application/json"))try{h=await p.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await p.text();let y={status:p.status,payload:h};if(!p.ok)if(404===p.status||403===p.status)throw new n(y);else if(401===p.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,o.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new i(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},d={get:(e,t)=>c("GET",e,t),post:(e,t,s)=>c("POST",e,{...s,body:t}),put:(e,t,s)=>c("PUT",e,{...s,body:t}),patch:(e,t,s)=>c("PATCH",e,{...s,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},36317:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(25656);s(40618).config();let a={login:e=>r.Ay.post("/api/auth/login",e),register:e=>r.Ay.post("/api/auth/signup",e),forgot:e=>r.Ay.put("/api/auth/app-forgot-pass",e),changepass:e=>r.Ay.put("/api/auth/app-reset-pass",e),auth:e=>r.Ay.post("/api/auth",e,{baseUrl:""}),checkCode:e=>r.Ay.get("/api/auth/check-code/".concat(e)),VerifyAppCode:e=>r.Ay.post("/api/auth/verify-app-code",e),VerifyCode:e=>r.Ay.post("/api/auth/verify-code",e),logoutFromNextServerToServer:e=>r.Ay.post("/api/auth/blacklist-token/",e,{headers:{Authorization:"Bearer ".concat(e.sessionToken)}}),logoutFromNextClientToNextServer:(e,t)=>r.Ay.post("/api/auth/logout",{force:e},{baseUrl:"",signal:t}),slideSessionFromNextServerToServer:e=>r.Ay.post("/auth/slide-session",{},{headers:{Authorization:"Bearer ".concat(e)}}),slideSessionFromNextClientToNextServer:()=>r.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},43281:(e,t,s)=>{"use strict";s.d(t,{lV:()=>u,MJ:()=>g,zB:()=>p,eI:()=>y,lR:()=>x,C5:()=>A});var r=s(95155),a=s(12115),o=s(46673),i=s(22544),n=s(64269),l=s(46554);let c=(0,s(83101).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.b,{ref:t,className:(0,n.cn)(c(),s),...a})});d.displayName=l.b.displayName;let u=i.Op,m=a.createContext({}),p=e=>{let{...t}=e;return(0,r.jsx)(m.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},h=()=>{let e=a.useContext(m),t=a.useContext(f),{getFieldState:s,formState:r}=(0,i.xW)(),o=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=t;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...o}},f=a.createContext({}),y=a.forwardRef((e,t)=>{let{className:s,...o}=e,i=a.useId();return(0,r.jsx)(f.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,n.cn)("mb-4",s),...o})})});y.displayName="FormItem";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e,{error:o,formItemId:i}=h();return(0,r.jsx)(d,{ref:t,className:(0,n.cn)(o&&"text-destructive",s),htmlFor:i,...a})});x.displayName="FormLabel";let g=a.forwardRef((e,t)=>{let{...s}=e,{error:a,formItemId:i,formDescriptionId:n,formMessageId:l}=h();return(0,r.jsx)(o.DX,{ref:t,id:i,"aria-describedby":a?"".concat(n," ").concat(l):"".concat(n),"aria-invalid":!!a,...s})});g.displayName="FormControl",a.forwardRef((e,t)=>{let{className:s,...a}=e,{formDescriptionId:o}=h();return(0,r.jsx)("p",{ref:t,id:o,className:(0,n.cn)("text-[0.8rem] text-muted-foreground",s),...a})}).displayName="FormDescription";let A=a.forwardRef((e,t)=>{let{className:s,children:a,...o}=e,{error:i,formMessageId:l}=h(),c=i?String(null==i?void 0:i.message):a;return c?(0,r.jsx)("p",{ref:t,id:l,className:(0,n.cn)("text-[0.8rem] font-medium text-red-600",s),...o,children:c}):null});A.displayName="FormMessage"},64269:(e,t,s)=>{"use strict";s.d(t,{Fd:()=>i,cn:()=>o}),s(25656);var r=s(2821),a=s(75889);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}s(138);let i=e=>e.startsWith("/")?e.slice(1):e},65142:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(95155),a=s(64269),o=s(88109),i=s(67553),n=s(12115);let l=n.forwardRef((e,t)=>{let{className:s,type:l,...c}=e,[d,u]=(0,n.useState)(!1);return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)("input",{type:"password"===l&&d?"text":l,autoComplete:"password"===l?"new-password":"",className:(0,a.cn)("input input-bordered w-full rounded-md",s),ref:t,...c}),"password"===l&&(d?(0,r.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}):(0,r.jsx)(i.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}))]})})});l.displayName="Input"},72016:()=>{},77873:(e,t,s)=>{"use strict";s.d(t,{I:()=>a});var r=s(23997);let a=async()=>{let e=await r.Ay.load();return(await e.get()).visitorId}},79902:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(77376),a=s(95704);let o=r.Ik({NEXT_PUBLIC_API_ENDPOINT:r.Yj().url(),NEXT_PUBLIC_URL:r.Yj().url(),CRYPTOJS_SECRECT:r.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!o.success)throw console.error("Invalid environment variables:",o.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=o.data},81560:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(95155),a=s(33602),o=s(22544),i=s(12115),n=s(43281),l=s(65142),c=s(95113),d=s(36317),u=s(20063),m=s(97367),p=s(56599),h=s(74744),f=s(1141),y=s(77873);let x=()=>{let[e,t]=(0,i.useState)(null),[s,x]=(0,i.useState)(!1),{setUser:g}=(0,m.U)(),[A,w]=(0,i.useState)(!1),v=(0,u.useRouter)(),[b,j]=(0,i.useState)(null);(0,i.useEffect)(()=>{w("localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname)},[]);let N=(0,o.mN)({resolver:(0,a.u)(c.Ap),defaultValues:{email:"",code:"",password:""}});async function C(r){if(!A&&!e)return void h.oR.error("Vui l\xf2ng x\xe1c nhận reCAPTCHA!");if(s)return;x(!0);let a=await (0,y.I)();try{await d.A.changepass({...r,deviceId:a}),h.oR.success("Change Password successful!"),t(null),v.push("/login"),v.refresh()}catch(e){h.oR.error("An error occurred during change Password. Please try again."),j(e.payload.message)}finally{x(!1)}}return(0,r.jsx)("div",{className:"flex flex-row content-center items-center justify-center max-w-4xl mx-auto overflow-y-auto px-4",children:(0,r.jsx)("div",{className:"w-full lg:w-7/12 md:px-4 my-10",children:(0,r.jsxs)("div",{className:"card shadow-xl bg-white dark:bg-midnight-second rounded-md p-8",children:[(0,r.jsx)("h1",{className:"text-2xl text-center mb-4",children:"Đặt lại mật khẩu của bạn"}),(0,r.jsx)(n.lV,{...N,children:(0,r.jsxs)("form",{onSubmit:N.handleSubmit(C),className:"max-w-[600px] flex-shrink-0 w-full",noValidate:!0,children:[(0,r.jsx)(n.zB,{control:N.control,name:"code",render:e=>{let{field:t}=e;return(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.MJ,{children:(0,r.jsx)(l.p,{placeholder:"code",...t})}),(0,r.jsx)(n.C5,{})]})}}),(0,r.jsx)(n.zB,{control:N.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.MJ,{children:(0,r.jsx)(l.p,{placeholder:"email",type:"email",...t})}),(0,r.jsx)(n.C5,{})]})}}),(0,r.jsx)(n.zB,{control:N.control,name:"password",render:e=>{let{field:t}=e;return(0,r.jsxs)(n.eI,{children:[(0,r.jsx)(n.MJ,{children:(0,r.jsx)(l.p,{placeholder:"Mật khẩu",type:"password",...t})}),(0,r.jsx)(n.C5,{})]})}}),(0,r.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:b}),!A&&(0,r.jsx)(f.A,{sitekey:"6LdfRaYrAAAAAJf8CSSwwy9RsyFybpunI65P2LTt",onChange:e=>t(e)}),(0,r.jsxs)("button",{disabled:!!s,type:"submit",className:"btn btn-primary bg-blue-700 w-40 text-white mx-auto flex items-center mt-6",children:[s?(0,r.jsx)(p.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})})]})})})}},95113:(e,t,s)=>{"use strict";s.d(t,{Ap:()=>n,ZZ:()=>l,aU:()=>a,ab:()=>i,iV:()=>o});var r=s(37424);let a=r.Ay.object({username:r.Ay.string().trim().min(2).max(256),email:r.Ay.string().email(),password:r.Ay.string().min(6).max(100),confirmPassword:r.Ay.string().min(6).max(100)}).strict().superRefine((e,t)=>{let{confirmPassword:s,password:r}=e;s!==r&&t.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})});r.Ay.object({token:r.Ay.string(),user:r.Ay.object({_id:r.Ay.number(),username:r.Ay.string(),email:r.Ay.string(),rule:r.Ay.string()}),message:r.Ay.string()});let o=r.Ay.object({email:r.Ay.string().email(),password:r.Ay.string().min(6).max(100),deviceId:r.Ay.string()}).strict(),i=r.Ay.object({email:r.Ay.string().email()}).strict(),n=r.Ay.object({email:r.Ay.string().email(),code:r.Ay.string(),password:r.Ay.string().min(6).max(100)}).strict();r.Ay.object({}).strict();let l=r.Ay.object({code:r.Ay.string().min(6),userId:r.Ay.string(),deviceId:r.Ay.string()}).strict();r.Ay.object({userId:r.Ay.string()}).strict()},97367:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,default:()=>n});var r=s(95155),a=s(12115);let o=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,a.useContext)(o),n=e=>{let{children:t}=e,[s,i]=(0,a.useState)(()=>null),[n,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),l(!1)},[i]),(0,r.jsx)(o.Provider,{value:{user:s,setUser:c,isAuthenticated:!!s,isLoading:n},children:t})}}},e=>{e.O(0,[9268,2739,4744,1804,6770,1141,8441,1255,7358],()=>e(e.s=25033)),_N_E=e.O()}]);