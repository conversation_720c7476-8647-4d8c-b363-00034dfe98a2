(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4736],{25656:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var s=r(79902),a=r(64269),n=r(20063);class o extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class l extends o{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let i=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?s.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,m=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),p=await fetch(m,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),h=null,f=p.headers.get("content-type");if(f&&f.includes("application/json"))try{h=await p.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await p.text();let x={status:p.status,payload:h};if(!p.ok)if(404===p.status||403===p.status)throw new l(x);else if(401===p.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,n.redirect)("/logout?sessionToken=".concat(e))}else if(!i){i=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await i}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),i=null,location.href="/login"}}}else throw new o(x);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return x},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},37424:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c});var s={};r.r(s),r.d(s,{BRAND:()=>l.qt,DIRTY:()=>n.jm,EMPTY_PATH:()=>n.I3,INVALID:()=>n.uY,NEVER:()=>l.tm,OK:()=>n.OK,ParseStatus:()=>n.MY,Schema:()=>l.Sj,ZodAny:()=>l.Ml,ZodArray:()=>l.n,ZodBigInt:()=>l.Lr,ZodBoolean:()=>l.WF,ZodBranded:()=>l.eN,ZodCatch:()=>l.hw,ZodDate:()=>l.aP,ZodDefault:()=>l.Xi,ZodDiscriminatedUnion:()=>l.jv,ZodEffects:()=>l.k1,ZodEnum:()=>l.Vb,ZodError:()=>i.G,ZodFirstPartyTypeKind:()=>l.kY,ZodFunction:()=>l.CZ,ZodIntersection:()=>l.Jv,ZodIssueCode:()=>i.eq,ZodLazy:()=>l.Ih,ZodLiteral:()=>l.DN,ZodMap:()=>l.Ut,ZodNaN:()=>l.Tq,ZodNativeEnum:()=>l.WM,ZodNever:()=>l.iS,ZodNull:()=>l.PQ,ZodNullable:()=>l.l1,ZodNumber:()=>l.rS,ZodObject:()=>l.bv,ZodOptional:()=>l.Ii,ZodParsedType:()=>o.Zp,ZodPipeline:()=>l._c,ZodPromise:()=>l.$i,ZodReadonly:()=>l.EV,ZodRecord:()=>l.b8,ZodSchema:()=>l.lK,ZodSet:()=>l.Kz,ZodString:()=>l.ND,ZodSymbol:()=>l.K5,ZodTransformer:()=>l.BG,ZodTuple:()=>l.y0,ZodType:()=>l.aR,ZodUndefined:()=>l._Z,ZodUnion:()=>l.fZ,ZodUnknown:()=>l._,ZodVoid:()=>l.a0,addIssueToContext:()=>n.zn,any:()=>l.bz,array:()=>l.YO,bigint:()=>l.o,boolean:()=>l.zM,coerce:()=>l.au,custom:()=>l.Ie,date:()=>l.p6,datetimeRegex:()=>l.fm,defaultErrorMap:()=>a.su,discriminatedUnion:()=>l.gM,effect:()=>l.QZ,enum:()=>l.k5,function:()=>l.fH,getErrorMap:()=>a.$W,getParsedType:()=>o.CR,instanceof:()=>l.Nl,intersection:()=>l.E$,isAborted:()=>n.G4,isAsync:()=>n.xP,isDirty:()=>n.DM,isValid:()=>n.fn,late:()=>l.fn,lazy:()=>l.RZ,literal:()=>l.eu,makeIssue:()=>n.y7,map:()=>l.Tj,nan:()=>l.oi,nativeEnum:()=>l.fc,never:()=>l.Zm,null:()=>l.ch,nullable:()=>l.me,number:()=>l.ai,object:()=>l.Ik,objectUtil:()=>o.o6,oboolean:()=>l.yN,onumber:()=>l.p7,optional:()=>l.lq,ostring:()=>l.Di,pipeline:()=>l.Tk,preprocess:()=>l.vk,promise:()=>l.iv,quotelessJson:()=>i.WI,record:()=>l.g1,set:()=>l.hZ,setErrorMap:()=>a.pJ,strictObject:()=>l.re,string:()=>l.Yj,symbol:()=>l.HR,transformer:()=>l.Gu,tuple:()=>l.PV,undefined:()=>l.Vx,union:()=>l.KC,unknown:()=>l.L5,util:()=>o.ZS,void:()=>l.rI});var a=r(12423),n=r(86625),o=r(88658),l=r(77376),i=r(44193);let c=s},38852:(e,t,r)=>{"use strict";r.d(t,{default:()=>N});var s=r(95155),a=r(33602),n=r(22544),o=r(43281),l=r(65142),i=r(20063),c=r(12115),d=r(37424);d.Ay.object({user:d.Ay.object({_id:d.Ay.number(),username:d.Ay.string(),email:d.Ay.string(),rule:d.Ay.string(),permissions:d.Ay.array(d.Ay.string()).optional()}),message:d.Ay.string()}).strict(),d.Ay.object({user:d.Ay.object({_id:d.Ay.number(),username:d.Ay.string(),email:d.Ay.string(),rule:d.Ay.string(),isMail:d.Ay.boolean(),isAuthApp:d.Ay.boolean(),permissions:d.Ay.array(d.Ay.string()).optional(),department:d.Ay.object({_id:d.Ay.string(),name:d.Ay.string(),code:d.Ay.string()}).optional(),departmentRole:d.Ay.string().optional()}),message:d.Ay.string()}).strict();let u=d.Ay.object({username:d.Ay.string().trim().min(2).max(256)}),m=d.Ay.object({password:d.Ay.string().min(6).max(100),newPassword:d.Ay.string().min(6).max(100),confirmPassword:d.Ay.string().min(6).max(100)}).superRefine((e,t)=>{let{confirmPassword:r,newPassword:s}=e;r!==s&&t.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})}),p=d.Ay.object({isMail:d.Ay.boolean()}),h=d.Ay.object({isAuthApp:d.Ay.boolean()}),f=d.Ay.object({token:d.Ay.string()});var x=r(25656);let y={updateMe:(e,t)=>x.Ay.put("/api/auth/user/profile",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMePassword:(e,t)=>x.Ay.put("/api/auth/change-pass",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMeAuth:(e,t)=>x.Ay.put("/api/auth/active-mail",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMeAuthApp:(e,t)=>x.Ay.put("/api/auth/active-authapp",e,{headers:{Authorization:"Bearer ".concat(t)}}),verifyToken:(e,t)=>x.Ay.put("/api/auth/verify-authapp",e,{headers:{Authorization:"Bearer ".concat(t)}})};var b=r(56599),g=r(74744),j=r(97367),A=r(15239);let N=e=>{let{profile:t}=e;console.log(t);let{setUser:r}=(0,j.U)(),[d,x]=(0,c.useState)(!1);(0,i.useRouter)();let[N,w]=(0,c.useState)(""),[v,I]=(0,c.useState)(""),[k,P]=(0,c.useState)(""),S=(0,c.useRef)(null),C=(0,n.mN)({resolver:(0,a.u)(u),defaultValues:{username:t.username}}),Z=(0,n.mN)({resolver:(0,a.u)(m),defaultValues:{newPassword:"",password:"",confirmPassword:""}}),R=(0,n.mN)({resolver:(0,a.u)(p),defaultValues:{isMail:t.isMail}}),T=(0,n.mN)({resolver:(0,a.u)(h),defaultValues:{isAuthApp:t.isAuthApp}}),M=(0,n.mN)({resolver:(0,a.u)(f),defaultValues:{token:""}});async function E(e){if(!d){x(!0);try{let t=localStorage.getItem("sessionToken")||"",s=await y.updateMe(e,t);s&&(r(s.payload.user),g.oR.success("Profile update successful!"))}catch(e){console.log(e),g.oR.error("An error occurred during update your profile. Please try again.")}finally{x(!1)}}}async function _(e){if(!d){x(!0);try{let t=localStorage.getItem("sessionToken")||"";await y.updateMePassword(e,t)&&g.oR.success("Profile update successful!")}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{x(!1)}}}async function F(e){if(!d){x(!0);try{let t=localStorage.getItem("sessionToken")||"";await y.updateMeAuth(e,t)&&g.oR.success("Profile update successful!")}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{x(!1)}}}async function O(e){if(!d){x(!0);try{let t=localStorage.getItem("sessionToken")||"",r=await y.updateMeAuthApp(e,t);r&&(w(r.payload.qrCode),I(r.payload.secret),S.current&&S.current.showModal())}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{x(!1)}}}async function B(e){if(!d){x(!0);try{let r=localStorage.getItem("sessionToken")||"";await y.verifyToken(e,r)&&(t.isAuthApp=!0,S.current&&S.current.close(),g.oR.success("Profile update successful!"))}catch(e){P("Invalid 2FA code")}finally{x(!1)}}}return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.lV,{...C,children:(0,s.jsxs)("form",{onSubmit:C.handleSubmit(E),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,s.jsx)(o.lR,{children:"Email"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"shadcn",type:"email",value:t.email,readOnly:!0})}),(0,s.jsx)(o.C5,{}),(0,s.jsx)(o.zB,{control:C.control,name:"username",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.eI,{children:[(0,s.jsx)(o.lR,{children:"Nick name"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"Nick name",type:"text",...t})}),(0,s.jsx)(o.C5,{})]})}}),(0,s.jsxs)("button",{disabled:!!d,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[d?(0,s.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,s.jsx)(o.lV,{...Z,children:(0,s.jsxs)("form",{onSubmit:Z.handleSubmit(_),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,s.jsx)(o.zB,{control:Z.control,name:"password",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.eI,{children:[(0,s.jsx)(o.lR,{children:"Mật khẩu cũ"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"password",type:"password",...t})}),(0,s.jsx)(o.C5,{})]})}}),(0,s.jsx)(o.zB,{control:Z.control,name:"newPassword",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.eI,{children:[(0,s.jsx)(o.lR,{children:"Mật khẩu mới"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"password",type:"password",...t})}),(0,s.jsx)(o.C5,{})]})}}),(0,s.jsx)(o.zB,{control:Z.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.eI,{children:[(0,s.jsx)(o.lR,{children:"X\xe1c nhận mật khẩu"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...t})}),(0,s.jsx)(o.C5,{})]})}}),(0,s.jsxs)("button",{disabled:!!d,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[d?(0,s.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,s.jsxs)(o.lV,{...R,children:[(0,s.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng Email"}),(0,s.jsxs)("form",{onSubmit:R.handleSubmit(F),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,s.jsx)("span",{className:"text-sm mb-4",children:'"K\xedch hoạt t\xednh năng bảo mật bằng Email: Một m\xe3 x\xe1c thực sẽ được gửi đến email của bạn khi đăng nhập."'}),(0,s.jsx)(o.zB,{control:R.control,name:"isMail",render:e=>{var t;let{field:r}=e;return(0,s.jsxs)(o.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,s.jsx)("span",{children:"K\xedch hoạt bảo mật Qua Mail"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)("input",{type:"checkbox",checked:null!=(t=r.value)&&t,onChange:e=>r.onChange(e.target.checked),className:"checkbox"})}),(0,s.jsx)(o.C5,{})]})}}),(0,s.jsxs)("button",{disabled:!!d,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[d?(0,s.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})]}),(0,s.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng App "}),(0,s.jsx)("dialog",{id:"my_modal_1",className:"modal ",ref:S,children:(0,s.jsxs)("div",{className:"modal-box relative ",children:[(0,s.jsx)("h3",{className:"text-lg font-bold",children:"Set Up Two-Factor Authentication"}),(0,s.jsx)("p",{className:"py-4",children:"Qu\xe9t m\xe3 QR b\xean dưới bằng ứng dụng x\xe1c thực của bạn (v\xed dụ: Google Authenticator, Authy).."}),N?(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(A.default,{src:N,alt:"QR Code",width:200,height:200,priority:!0})}):(0,s.jsx)("p",{children:"Loading QR code..."}),(0,s.jsx)(o.lV,{...M,children:(0,s.jsxs)("form",{onSubmit:M.handleSubmit(B),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,s.jsx)(o.zB,{control:M.control,name:"token",render:e=>{let{field:t}=e;return(0,s.jsxs)(o.eI,{children:[(0,s.jsx)(o.lR,{children:"Nhập M\xe3 2FA Code"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)(l.p,{placeholder:"2FA Cod",type:"text",...t})}),(0,s.jsx)(o.C5,{})]})}}),k&&(0,s.jsx)("p",{className:"text-red-500 mt-2",children:k}),(0,s.jsxs)("button",{disabled:!!d,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[d?(0,s.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,s.jsx)("form",{method:"dialog",children:(0,s.jsx)("button",{className:"btn btn-sm btn-circle btn-ghost absolute right-2 top-2",children:"✕"})})]})}),(0,s.jsx)(o.lV,{...T,children:(0,s.jsxs)("form",{onSubmit:T.handleSubmit(O),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,s.jsx)("span",{className:"text-sm mb-4",children:'"Khi bật 2FA, mỗi lần đăng nhập, bạn sẽ được y\xeau cầu sử dụng kh\xf3a bảo mật, nhập m\xe3 x\xe1c minh hoặc x\xe1c nhận đăng nhập từ thiết bị di động, t\xf9y theo phương thức bạn đ\xe3 chọn."'}),(0,s.jsx)(o.zB,{control:T.control,name:"isAuthApp",render:e=>{var t;let{field:r}=e;return(0,s.jsxs)(o.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,s.jsx)("span",{children:"K\xedch hoạt bảo mật Qua ứng dụng"}),(0,s.jsx)(o.MJ,{children:(0,s.jsx)("input",{type:"checkbox",checked:null!=(t=r.value)&&t,onChange:e=>r.onChange(e.target.checked),className:"checkbox"})}),(0,s.jsx)(o.C5,{})]})}}),t.isAuthApp?null:(0,s.jsxs)("button",{disabled:!!d,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[d?(0,s.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})})]})}},43281:(e,t,r)=>{"use strict";r.d(t,{lV:()=>u,MJ:()=>b,zB:()=>p,eI:()=>x,lR:()=>y,C5:()=>g});var s=r(95155),a=r(12115),n=r(46673),o=r(22544),l=r(64269),i=r(46554);let c=(0,r(83101).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.b,{ref:t,className:(0,l.cn)(c(),r),...a})});d.displayName=i.b.displayName;let u=o.Op,m=a.createContext({}),p=e=>{let{...t}=e;return(0,s.jsx)(m.Provider,{value:{name:t.name},children:(0,s.jsx)(o.xI,{...t})})},h=()=>{let e=a.useContext(m),t=a.useContext(f),{getFieldState:r,formState:s}=(0,o.xW)(),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},f=a.createContext({}),x=a.forwardRef((e,t)=>{let{className:r,...n}=e,o=a.useId();return(0,s.jsx)(f.Provider,{value:{id:o},children:(0,s.jsx)("div",{ref:t,className:(0,l.cn)("mb-4",r),...n})})});x.displayName="FormItem";let y=a.forwardRef((e,t)=>{let{className:r,...a}=e,{error:n,formItemId:o}=h();return(0,s.jsx)(d,{ref:t,className:(0,l.cn)(n&&"text-destructive",r),htmlFor:o,...a})});y.displayName="FormLabel";let b=a.forwardRef((e,t)=>{let{...r}=e,{error:a,formItemId:o,formDescriptionId:l,formMessageId:i}=h();return(0,s.jsx)(n.DX,{ref:t,id:o,"aria-describedby":a?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!a,...r})});b.displayName="FormControl",a.forwardRef((e,t)=>{let{className:r,...a}=e,{formDescriptionId:n}=h();return(0,s.jsx)("p",{ref:t,id:n,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",r),...a})}).displayName="FormDescription";let g=a.forwardRef((e,t)=>{let{className:r,children:a,...n}=e,{error:o,formMessageId:i}=h(),c=o?String(null==o?void 0:o.message):a;return c?(0,s.jsx)("p",{ref:t,id:i,className:(0,l.cn)("text-[0.8rem] font-medium text-red-600",r),...n,children:c}):null});g.displayName="FormMessage"},64269:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>o,cn:()=>n}),r(25656);var s=r(2821),a=r(75889);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}r(138);let o=e=>e.startsWith("/")?e.slice(1):e},65142:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(95155),a=r(64269),n=r(88109),o=r(67553),l=r(12115);let i=l.forwardRef((e,t)=>{let{className:r,type:i,...c}=e,[d,u]=(0,l.useState)(!1);return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"relative w-full",children:[(0,s.jsx)("input",{type:"password"===i&&d?"text":i,autoComplete:"password"===i?"new-password":"",className:(0,a.cn)("input input-bordered w-full rounded-md",r),ref:t,...c}),"password"===i&&(d?(0,s.jsx)(n.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}):(0,s.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>u(!d)}))]})})});i.displayName="Input"},70832:(e,t,r)=>{Promise.resolve().then(r.bind(r,38852))},79902:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(77376),a=r(95704);let n=s.Ik({NEXT_PUBLIC_API_ENDPOINT:s.Yj().url(),NEXT_PUBLIC_URL:s.Yj().url(),CRYPTOJS_SECRECT:s.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!n.success)throw console.error("Invalid environment variables:",n.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let o=n.data},83011:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return a}});let s=r(12115);function a(e,t){let r=(0,s.useRef)(null),a=(0,s.useRef)(null);return(0,s.useCallback)(s=>{if(null===s){let e=r.current;e&&(r.current=null,e());let t=a.current;t&&(a.current=null,t())}else e&&(r.current=n(e,s)),t&&(a.current=n(t,s))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97367:(e,t,r)=>{"use strict";r.d(t,{U:()=>o,default:()=>l});var s=r(95155),a=r(12115);let n=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),o=()=>(0,a.useContext)(n),l=e=>{let{children:t}=e,[r,o]=(0,a.useState)(()=>null),[l,i]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{o(e),localStorage.setItem("user",JSON.stringify(e))},[o]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");o(e?JSON.parse(e):null),i(!1)},[o]),(0,s.jsx)(n.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:l},children:t})}}},e=>{e.O(0,[9268,2739,4744,7325,5239,8441,1255,7358],()=>e(e.s=70832)),_N_E=e.O()}]);