(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3948],{30486:(e,s,t)=>{Promise.resolve().then(t.bind(t,58207))},58207:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>J});var n=t(95155),a=t(12115),l=t(35626),r=t(80534),i=t(20063),c=t(74744),o=t(89715),d=t(37107),g=t(16485),x=t(26983),h=t(61194),m=t(80003),u=t(91761),b=t(19204),p=t(23664),y=t(69141),f=t(50859),j=t(14285),N=t(15870),v=t(6191),w=t(57828),C=t(49476),k=t(70532),T=t(11010),D=t(71360),A=t(57052),S=t(84961),z=t(65229);let I=[{value:"text",label:"Văn bản",icon:"\uD83D\uDCDD"},{value:"number",label:"Số",icon:"\uD83D\uDD22"},{value:"date",label:"Ng\xe0y",icon:"\uD83D\uDCC5"},{value:"datetime",label:"Ng\xe0y giờ",icon:"\uD83D\uDD50"},{value:"boolean",label:"Đ\xfang/Sai",icon:"☑️"},{value:"select",label:"Lựa chọn đơn",icon:"\uD83D\uDCCB"},{value:"multiselect",label:"Lựa chọn nhiều",icon:"\uD83D\uDCCB"},{value:"currency",label:"Tiền tệ",icon:"\uD83D\uDCB0"},{value:"percentage",label:"Phần trăm",icon:"\uD83D\uDCCA"},{value:"email",label:"Email",icon:"\uD83D\uDCE7"},{value:"phone",label:"Số điện thoại",icon:"\uD83D\uDCDE"},{value:"url",label:"Đường dẫn",icon:"\uD83D\uDD17"},{value:"textarea",label:"Văn bản d\xe0i",icon:"\uD83D\uDCC4"}];function L(e){let{isOpen:s,onClose:t,onSubmit:l,targetModel:r,existingFields:i=[]}=e,[c,o]=(0,a.useState)({name:"",label:"",description:"",dataType:"text",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[d,g]=(0,a.useState)({value:"",label:"",color:"#3B82F6"}),x=e=>i.some(s=>s.name.toLowerCase()===e.toLowerCase()),h="select"===c.dataType||"multiselect"===c.dataType;return s?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo trường t\xf9y chỉnh mới"}),(0,n.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,n.jsx)(z.A,{size:20})})]}),(0,n.jsxs)("form",{onSubmit:e=>(e.preventDefault(),c.name.trim()&&c.label.trim())?/^[a-zA-Z][a-zA-Z0-9_]*$/.test(c.name)?i.find(e=>e.name.toLowerCase()===c.name.toLowerCase())?void alert('T\xean trường "'.concat(c.name,'" đ\xe3 tồn tại. Vui l\xf2ng chọn t\xean kh\xe1c.')):void l(c):void alert("T\xean trường chỉ được chứa chữ c\xe1i, số v\xe0 dấu gạch dưới, bắt đầu bằng chữ c\xe1i"):void alert("Vui l\xf2ng nhập t\xean v\xe0 nh\xe3n hiển thị"),className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xean trường ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:c.name,onChange:e=>o(s=>({...s,name:e.target.value})),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ".concat(c.name&&x(c.name)?"border-red-300 focus:ring-red-500 bg-red-50":"border-gray-300 focus:ring-blue-500"),placeholder:"vd: priority_level",required:!0}),c.name&&x(c.name)?(0,n.jsx)("p",{className:"text-xs text-red-500 mt-1",children:"⚠️ T\xean trường n\xe0y đ\xe3 tồn tại. Vui l\xf2ng chọn t\xean kh\xe1c."}):(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Chỉ chữ c\xe1i, số v\xe0 dấu gạch dưới. Bắt đầu bằng chữ c\xe1i."})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:c.label,onChange:e=>o(s=>({...s,label:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,n.jsx)("textarea",{value:c.description,onChange:e=>o(s=>({...s,description:e.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Kiểu dữ liệu ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:I.map(e=>(0,n.jsx)("button",{type:"button",onClick:()=>o(s=>({...s,dataType:e.value})),className:"p-3 border rounded-lg text-left transition-colors ".concat(c.dataType===e.value?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"),children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-lg",children:e.icon}),(0,n.jsx)("span",{className:"text-sm font-medium",children:e.label})]})},e.value))})]}),h&&(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["T\xf9y chọn ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,n.jsx)("input",{type:"text",value:d.value,onChange:e=>g(s=>({...s,value:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,n.jsx)("input",{type:"text",value:d.label,onChange:e=>g(s=>({...s,label:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,n.jsx)("input",{type:"color",value:d.color,onChange:e=>g(s=>({...s,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.value.trim()&&d.label.trim()?c.config.options.some(e=>e.value===d.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(o(e=>({...e,config:{...e.config,options:[...e.config.options,{...d}]}})),g({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,n.jsx)(v.A,{size:16})})]}),(0,n.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:c.config.options.map((e,s)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:e.color}}),(0,n.jsx)("span",{className:"text-sm font-medium",children:e.label}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.value,")"]}),(0,n.jsx)("button",{type:"button",onClick:()=>{o(e=>({...e,config:{...e.config,options:e.config.options.filter((e,t)=>t!==s)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,n.jsx)(D.A,{size:14})})]},s))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.required,onChange:e=>o(s=>({...s,config:{...s.config,required:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.showInList,onChange:e=>o(s=>({...s,config:{...s.config,showInList:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.showInStats,onChange:e=>o(s=>({...s,config:{...s.config,showInStats:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo trường"})]})]})]})}):null}function E(e){var s;let{isOpen:t,onClose:l,onSubmit:r,field:i}=e,[c,o]=(0,a.useState)({label:"",description:"",config:{required:!1,showInList:!0,showInStats:!0,columnWidth:150,options:[]}}),[d,g]=(0,a.useState)({value:"",label:"",color:"#3B82F6"});(0,a.useEffect)(()=>{i&&o({label:i.label,description:i.description||"",config:{required:i.config.required||!1,showInList:!1!==i.config.showInList,showInStats:!1!==i.config.showInStats,columnWidth:i.config.columnWidth||150,options:i.config.options||[]}})},[i]);let x=(null==i?void 0:i.dataType)==="select"||(null==i?void 0:i.dataType)==="multiselect";return t&&i?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa trường"}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,n.jsx)("span",{className:"text-lg",children:{text:"\uD83D\uDCDD",number:"\uD83D\uDD22",date:"\uD83D\uDCC5",datetime:"\uD83D\uDD50",boolean:"☑️",select:"\uD83D\uDCCB",multiselect:"\uD83D\uDCCB",currency:"\uD83D\uDCB0",percentage:"\uD83D\uDCCA",email:"\uD83D\uDCE7",phone:"\uD83D\uDCDE",url:"\uD83D\uDD17",textarea:"\uD83D\uDCC4",file:"\uD83D\uDCCE",json:"\uD83D\uDD27"}[i.dataType]||"❓"}),(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[i.name," (",{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[s=i.dataType]||s,")"]}),(0,n.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(i.isBuiltIn?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"),children:i.isBuiltIn?"Mặc định":"Đ\xe3 th\xeam"})]})]}),(0,n.jsx)("button",{onClick:l,className:"p-2 text-gray-400 hover:text-gray-600 rounded-lg",children:(0,n.jsx)(z.A,{size:20})})]}),(0,n.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),!c.label.trim())return void alert("Vui l\xf2ng nhập nh\xe3n hiển thị");r(c)},className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Nh\xe3n hiển thị ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:c.label,onChange:e=>o(s=>({...s,label:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"vd: Mức độ ưu ti\xean",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"M\xf4 tả"}),(0,n.jsx)("textarea",{value:c.description,onChange:e=>o(s=>({...s,description:e.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"M\xf4 tả về trường n\xe0y..."})]}),x&&!i.isBuiltIn&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xf9y chọn"}),(0,n.jsxs)("div",{className:"flex gap-2 mb-3",children:[(0,n.jsx)("input",{type:"text",value:d.value,onChange:e=>g(s=>({...s,value:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Gi\xe1 trị (vd: high)"}),(0,n.jsx)("input",{type:"text",value:d.label,onChange:e=>g(s=>({...s,label:e.target.value})),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Nh\xe3n hiển thị (vd: Cao)"}),(0,n.jsx)("input",{type:"color",value:d.color,onChange:e=>g(s=>({...s,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded-lg"}),(0,n.jsx)("button",{type:"button",onClick:()=>d.value.trim()&&d.label.trim()?c.config.options.some(e=>e.value===d.value)?void alert("Gi\xe1 trị n\xe0y đ\xe3 tồn tại"):void(o(e=>({...e,config:{...e.config,options:[...e.config.options,{...d}]}})),g({value:"",label:"",color:"#3B82F6"})):void alert("Vui l\xf2ng nhập gi\xe1 trị v\xe0 nh\xe3n hiển thị"),className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:(0,n.jsx)(v.A,{size:16})})]}),(0,n.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:c.config.options.map((e,s)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded",style:{backgroundColor:e.color}}),(0,n.jsx)("span",{className:"text-sm font-medium",children:e.label}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.value,")"]}),(0,n.jsx)("button",{type:"button",onClick:()=>{o(e=>({...e,config:{...e.config,options:e.config.options.filter((e,t)=>t!==s)}}))},className:"ml-auto p-1 text-red-600 hover:bg-red-50 rounded",children:(0,n.jsx)(D.A,{size:14})})]},s))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"C\xe0i đặt"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.required,onChange:e=>o(s=>({...s,config:{...s.config,required:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Bắt buộc nhập"})]}),(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.showInList,onChange:e=>o(s=>({...s,config:{...s.config,showInList:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Hiển thị trong danh s\xe1ch"})]}),(0,n.jsxs)("label",{className:"flex items-center gap-2",children:[(0,n.jsx)("input",{type:"checkbox",checked:c.config.showInStats,onChange:e=>o(s=>({...s,config:{...s.config,showInStats:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"text-sm",children:"Hiển thị trong thống k\xea"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Độ rộng cột (pixels)"}),(0,n.jsx)("input",{type:"number",value:c.config.columnWidth,onChange:e=>o(s=>({...s,config:{...s.config,columnWidth:parseInt(e.target.value)||150}})),className:"w-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",min:"50",max:"500"})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:l,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})]})}):null}var _=t(75444);function B(e){let{targetModel:s="CourtCase",onFieldsChange:t}=e,[l,r]=(0,a.useState)([]),[i,z]=(0,a.useState)([]),[I,B]=(0,a.useState)(null),[H,R]=(0,a.useState)([]),[F,M]=(0,a.useState)(!0),[V,q]=(0,a.useState)(!1),[K,O]=(0,a.useState)(null),[X,G]=(0,a.useState)(null),[W,P]=(0,a.useState)(""),[Q,Z]=(0,a.useState)("all"),[J,$]=(0,a.useState)(!1),{hasPermission:U}=(0,_.S)();(0,a.useEffect)(()=>{Y()},[s]);let Y=async()=>{try{M(!0);let a=localStorage.getItem("sessionToken")||"",[l,i,c]=await Promise.all([A.A.getCustomFields(s,a),S.A.getFieldConfiguration(s,a),A.A.getFieldsInDatabase(s).catch(()=>({payload:{data:{fields:[]}}}))]);if(l.payload.success&&i.payload.success){var e,n;r(l.payload.fields),B(i.payload.configuration),(null==(n=c.payload)||null==(e=n.data)?void 0:e.fields)&&R(c.payload.data.fields);let s=[];l.payload.fields.forEach(e=>{s.push({...e,canEdit:!0,canDelete:!0})}),s.sort((e,s)=>e.config.sortOrder-s.config.sortOrder),z(s),null==t||t(l.payload.fields)}}catch(e){console.error("Error fetching fields and configuration:",e),c.oR.error("Lỗi khi tải danh s\xe1ch trường t\xf9y chỉnh")}finally{M(!1)}},ee=async e=>{try{let t=localStorage.getItem("sessionToken")||"",n=await A.A.createCustomField({...e,targetModel:s},t);n.payload.success?(c.oR.success("Tạo trường t\xf9y chỉnh th\xe0nh c\xf4ng"),q(!1),Y()):c.oR.error(n.payload.message||"Kh\xf4ng thể tạo trường t\xf9y chỉnh")}catch(e){var t;console.error("Error creating custom field:",e),(null==(t=e.payload)?void 0:t.message)?c.oR.error(e.payload.message):e.message?c.oR.error(e.message):c.oR.error("Lỗi khi tạo trường t\xf9y chỉnh")}},es=async(e,s)=>{try{let t=localStorage.getItem("sessionToken")||"",n=await A.A.updateCustomField(e,s,t);n.payload.success?(c.oR.success("Cập nhật trường t\xf9y chỉnh th\xe0nh c\xf4ng"),O(null),Y()):c.oR.error(n.payload.message||"Kh\xf4ng thể cập nhật trường t\xf9y chỉnh")}catch(e){console.error("Error updating custom field:",e),c.oR.error("Lỗi khi cập nhật trường t\xf9y chỉnh")}},et=async e=>{let s=e.isDefault?"trường cơ bản":"trường t\xf9y chỉnh";if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a ".concat(s,' "').concat(e.label,'"? Dữ liệu đ\xe3 nhập sẽ kh\xf4ng bị mất nhưng trường sẽ kh\xf4ng hiển thị nữa.')))try{if(e.isDefault){let t=i.filter(s=>s._id!==e._id);z(t),c.oR.success("X\xf3a ".concat(s," th\xe0nh c\xf4ng"))}else{let s=localStorage.getItem("sessionToken")||"",t=await A.A.deleteCustomField(e._id,s);t.payload.success?(c.oR.success("X\xf3a trường t\xf9y chỉnh th\xe0nh c\xf4ng"),Y()):c.oR.error(t.payload.message||"Kh\xf4ng thể x\xf3a trường t\xf9y chỉnh")}}catch(e){console.error("Error deleting field:",e),c.oR.error("Lỗi khi x\xf3a ".concat(s))}},en=async e=>{try{await es(e._id,{config:{...e.config,showInList:!e.config.showInList}})}catch(e){console.error("Error toggling field visibility:",e),c.oR.error("Lỗi khi cập nhật hiển thị trường")}},ea=e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},el=async(e,t)=>{if(e.preventDefault(),!X||X===t)return void G(null);let n=i.findIndex(e=>e._id===X),a=i.findIndex(e=>e._id===t);if(-1===n||-1===a)return void G(null);let l=[...i],[r]=l.splice(n,1);l.splice(a,0,r);let o=l.map((e,s)=>({fieldName:e.name,sortOrder:s}));try{let e=localStorage.getItem("sessionToken")||"";await S.A.updateFieldOrder({targetModel:s,fieldOrders:o},e);let t=l.filter(e=>!e.isDefault).map((e,s)=>({id:e._id,sortOrder:s}));t.length>0&&await A.A.updateFieldsOrder(t,e),await Y(),c.oR.success("Cập nhật thứ tự trường th\xe0nh c\xf4ng")}catch(e){console.error("Error updating field order:",e),c.oR.error("Lỗi khi cập nhật thứ tự trường")}G(null)},er=i.filter(e=>{let s=""===W||e.name.toLowerCase().includes(W.toLowerCase())||e.label.toLowerCase().includes(W.toLowerCase()),t="all"===Q||"custom"===Q||"visible"===Q&&e.config.showInList||"hidden"===Q&&!e.config.showInList;return s&&t});return F?(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(N.A,{size:20}),"Quản l\xfd trường t\xf9y chỉnh"]}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho ","CourtCase"===s?"vụ việc t\xf2a \xe1n":s]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("button",{onClick:()=>$(!J),className:"flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,n.jsx)(o.A,{size:16}),J?"Ẩn th\xf4ng tin DB":"Xem th\xf4ng tin DB"]}),U("custom_fields_create")&&(0,n.jsxs)("button",{onClick:()=>q(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(v.A,{size:16}),"Th\xeam trường mới"]})]})]}),i.length>0&&(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,n.jsx)(N.A,{size:20,className:"text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:i.length}),(0,n.jsx)("div",{className:"text-sm text-blue-700",children:"Tổng số trường"})]})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,n.jsx)(v.A,{size:20,className:"text-green-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-900",children:i.filter(e=>!e.isDefault).length}),(0,n.jsx)("div",{className:"text-sm text-green-700",children:"Trường đ\xe3 th\xeam"})]})]})}),(0,n.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,n.jsx)(w.A,{size:20,className:"text-purple-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:i.filter(e=>e.config.showInList).length}),(0,n.jsx)("div",{className:"text-sm text-purple-700",children:"Đang hiển thị"})]})]})})]}),J&&H.length>0&&(0,n.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("h4",{className:"text-md font-semibold text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(o.A,{size:18}),"Trường đang c\xf3 trong Database (",H.length,")"]}),(0,n.jsx)("button",{onClick:()=>$(!1),className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto",children:H.map((e,s)=>(0,n.jsxs)("div",{className:"bg-white p-3 rounded border",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"font-medium text-sm text-gray-900",children:e.name}),(0,n.jsxs)("div",{className:"flex items-center gap-1",children:[e.hasConfig?(0,n.jsx)("span",{className:"px-2 py-1 text-xs bg-green-100 text-green-800 rounded",children:"C\xf3 config"}):(0,n.jsx)("span",{className:"px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded",children:"Kh\xf4ng config"}),!e.isActive&&(0,n.jsx)("span",{className:"px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"Inactive"})]})]}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 mb-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Nh\xe3n:"})," ",e.label]}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 mb-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Kiểu:"})," ",e.dataType]}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 mb-2",children:[(0,n.jsx)("span",{className:"font-medium",children:"Sử dụng:"})," ",e.usageCount,"/",e.totalDocuments,"(",(e.usageCount/e.totalDocuments*100).toFixed(1),"%)"]}),e.sampleValues&&e.sampleValues.length>0&&(0,n.jsxs)("div",{className:"text-xs text-gray-500",children:[(0,n.jsx)("span",{className:"font-medium",children:"Mẫu:"})," ",e.sampleValues.slice(0,2).join(", "),e.sampleValues.length>2&&"..."]})]},s))})]}),i.length>0&&(0,n.jsxs)("div",{className:"flex flex-wrap gap-2 mb-6",children:[(0,n.jsxs)("button",{onClick:()=>{z(i.map(e=>({...e,config:{...e.config,showInList:!0}}))),c.oR.success("Hiển thị tất cả trường th\xe0nh c\xf4ng")},className:"flex items-center gap-1 px-3 py-1 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,n.jsx)(w.A,{size:14}),"Hiển thị tất cả"]}),(0,n.jsx)("button",{onClick:()=>{z(i.map(e=>({...e,config:{...e.config,showInList:!1}}))),c.oR.success("Ẩn tất cả trường th\xe0nh c\xf4ng")},className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"\uD83D\uDE48 Ẩn tất cả"})]}),i.length>0&&(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("input",{type:"text",placeholder:"T\xecm kiếm trường theo t\xean hoặc nh\xe3n...",value:W,onChange:e=>P(e.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,n.jsxs)("select",{value:Q,onChange:e=>Z(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"all",children:"Tất cả trường"}),(0,n.jsx)("option",{value:"custom",children:"Chỉ trường đ\xe3 th\xeam"}),(0,n.jsx)("option",{value:"visible",children:"Chỉ trường hiển thị"}),(0,n.jsx)("option",{value:"hidden",children:"Chỉ trường ẩn"})]})]}),0===i.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(N.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,n.jsx)("p",{className:"text-gray-500 mb-4",children:"Đang tải danh s\xe1ch trường..."})]}):0===er.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(N.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,n.jsx)("p",{className:"text-gray-500 mb-4",children:"Kh\xf4ng t\xecm thấy trường n\xe0o ph\xf9 hợp"}),(0,n.jsx)("button",{onClick:()=>{P(""),Z("all")},className:"text-blue-600 hover:text-blue-700 font-medium",children:"X\xf3a bộ lọc"})]}):(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 mb-4",children:(0,n.jsxs)("div",{className:"flex items-start gap-2",children:[(0,n.jsx)("span",{className:"text-green-500 mt-0.5",children:"ℹ️"}),(0,n.jsxs)("div",{className:"text-sm text-green-700",children:[(0,n.jsx)("strong",{children:"Hướng dẫn:"})," Tất cả c\xe1c trường đều c\xf3 thể k\xe9o thả để sắp xếp thứ tự. Trường cơ bản (m\xe0u xanh dương) v\xe0 trường t\xf9y chỉnh (m\xe0u xanh l\xe1) đều c\xf3 thể di chuyển."]})]})}),er.map(e=>{var s;return(0,n.jsxs)("div",{draggable:!0,onDragStart:s=>{G(e._id),s.dataTransfer.effectAllowed="move"},onDragOver:ea,onDrop:s=>el(s,e._id),className:"flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-move ".concat(X===e._id?"opacity-50":""," ").concat(e.config.showInList?"border-gray-300":"bg-gray-50 border-gray-200"," border-l-4 border-l-green-500"),children:[(0,n.jsx)(C.A,{size:16,className:"text-gray-400",title:"K\xe9o thả để sắp xếp thứ tự"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"text":return(0,n.jsx)(o.A,{size:16,className:"text-blue-500"});case"number":return(0,n.jsx)(d.A,{size:16,className:"text-green-500"});case"date":return(0,n.jsx)(g.A,{size:16,className:"text-purple-500"});case"datetime":return(0,n.jsx)(x.A,{size:16,className:"text-purple-600"});case"boolean":return(0,n.jsx)(h.A,{size:16,className:"text-orange-500"});case"select":return(0,n.jsx)(m.A,{size:16,className:"text-indigo-500"});case"multiselect":return(0,n.jsx)(m.A,{size:16,className:"text-indigo-600"});case"currency":return(0,n.jsx)(u.A,{size:16,className:"text-green-600"});case"percentage":return(0,n.jsx)(b.A,{size:16,className:"text-yellow-500"});case"email":return(0,n.jsx)(p.A,{size:16,className:"text-red-500"});case"phone":return(0,n.jsx)(y.A,{size:16,className:"text-blue-600"});case"url":return(0,n.jsx)(f.A,{size:16,className:"text-cyan-500"});case"textarea":return(0,n.jsx)(o.A,{size:16,className:"text-gray-500"});case"file":return(0,n.jsx)(j.A,{size:16,className:"text-gray-600"});case"json":return(0,n.jsx)(N.A,{size:16,className:"text-gray-700"});default:return(0,n.jsx)(o.A,{size:16,className:"text-gray-400"})}})(e.dataType)}),(0,n.jsx)("span",{className:"font-medium text-gray-900",children:e.label}),(0,n.jsx)("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:{text:"Văn bản",number:"Số",date:"Ng\xe0y",datetime:"Ng\xe0y giờ",boolean:"Đ\xfang/Sai",select:"Lựa chọn đơn",multiselect:"Lựa chọn nhiều",currency:"Tiền tệ",percentage:"Phần trăm",email:"Email",phone:"Số điện thoại",url:"Đường dẫn",textarea:"Văn bản d\xe0i",file:"File đ\xednh k\xe8m",json:"Dữ liệu JSON"}[s=e.dataType]||s}),e.config.required&&(0,n.jsx)("span",{className:"text-xs bg-red-100 text-red-600 px-2 py-1 rounded",children:"Bắt buộc"}),(0,n.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(e.isDefault?"bg-blue-100 text-blue-600":"bg-green-100 text-green-600"),children:e.isDefault?"Cơ bản":"Đ\xe3 th\xeam"})]}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("button",{onClick:()=>en(e),className:"p-2 rounded-lg transition-colors ".concat(e.config.showInList?"text-green-600 hover:bg-green-50":"text-gray-400 hover:bg-gray-100"),title:e.config.showInList?"Ẩn khỏi danh s\xe1ch":"Hiển thị trong danh s\xe1ch",children:e.config.showInList?(0,n.jsx)(w.A,{size:16}):(0,n.jsx)(k.A,{size:16})}),U("custom_fields_edit")&&(0,n.jsx)("button",{onClick:()=>O(e),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,n.jsx)(T.A,{size:16})}),U("custom_fields_delete")&&(0,n.jsx)("button",{onClick:()=>et(e),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,n.jsx)(D.A,{size:16})})]})]},e._id)}),0===i.length&&(0,n.jsxs)("div",{className:"text-center py-6 border-2 border-dashed border-gray-300 rounded-lg mt-4",children:[(0,n.jsx)("p",{className:"text-gray-500 mb-2",children:"Chưa c\xf3 trường n\xe0o được th\xeam"}),U("custom_fields_create")&&(0,n.jsx)("button",{onClick:()=>q(!0),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Tạo trường đầu ti\xean"})]})]}),(0,n.jsx)(L,{isOpen:V,onClose:()=>q(!1),onSubmit:ee,targetModel:s,existingFields:l.map(e=>({name:e.name,label:e.label}))}),K&&(0,n.jsx)(E,{isOpen:!0,onClose:()=>O(null),onSubmit:e=>{es(K._id,e)},field:K})]})}var H=t(87454),R=t(1524),F=t(50906);function M(e){let{targetModel:s="CourtCase",fields:t}=e,[l,i]=(0,a.useState)({}),[o,d]=(0,a.useState)(!0);(0,a.useEffect)(()=>{g()},[s]);let g=async()=>{try{d(!0);let e=localStorage.getItem("sessionToken")||"",t=await A.A.getCustomFieldStats(s,e);t.payload.success&&i(t.payload.stats)}catch(e){console.error("Error fetching custom field stats:",e),c.oR.error("Lỗi khi tải thống k\xea trường t\xf9y chỉnh")}finally{d(!1)}},x=e=>new Intl.NumberFormat("vi-VN").format(e),h=e=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(e);if(o)return(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})});let m=Object.entries(l);return 0===m.length?(0,n.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(r.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,n.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 thống k\xea cho trường t\xf9y chỉnh"}),(0,n.jsx)("p",{className:"text-sm text-gray-400",children:"Th\xeam dữ liệu v\xe0o c\xe1c trường t\xf9y chỉnh để xem thống k\xea"})]})}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,n.jsx)(r.A,{size:24,className:"text-blue-600"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Thống k\xea trường t\xf9y chỉnh"})]}),(0,n.jsx)("p",{className:"text-gray-600",children:"Thống k\xea tự động được t\xednh to\xe1n dựa tr\xean dữ liệu trong c\xe1c trường t\xf9y chỉnh"})]}),(0,n.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:m.map(e=>{let[s,a]=e,l=t.find(e=>e.name===s);return(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,n.jsxs)("div",{className:"p-2 bg-blue-100 rounded-lg",children:["distribution"===a.type&&(0,n.jsx)(H.A,{size:20,className:"text-blue-600"}),"numeric"===a.type&&(0,n.jsx)(R.A,{size:20,className:"text-blue-600"}),"boolean"===a.type&&(0,n.jsx)(F.A,{size:20,className:"text-blue-600"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-gray-900",children:a.label}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:(null==l?void 0:l.dataType)==="currency"?"Tiền tệ":(null==l?void 0:l.dataType)==="percentage"?"Phần trăm":(null==l?void 0:l.dataType)==="select"?"Lựa chọn đơn":(null==l?void 0:l.dataType)==="multiselect"?"Lựa chọn nhiều":(null==l?void 0:l.dataType)==="boolean"?"Đ\xfang/Sai":(null==l?void 0:l.dataType)==="number"?"Số":"Kh\xe1c"})]})]}),"distribution"===a.type&&Array.isArray(a.data)&&(0,n.jsx)("div",{className:"space-y-3",children:a.data.map((e,s)=>{let t=a.data.reduce((e,s)=>e+s.count,0),l=t>0?e.count/t*100:0;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full bg-blue-500"}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e._id||"Kh\xf4ng x\xe1c định"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:x(e.count)}),(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["(",l.toFixed(1),"%)"]})]})]},s)})}),"numeric"===a.type&&a.data&&(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?h(a.data.total||0):x(a.data.total||0)}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng"})]}),(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?h(a.data.avg||0):x(a.data.avg||0)}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Trung b\xecnh"})]}),(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?h(a.data.min||0):x(a.data.min||0)}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Nhỏ nhất"})]}),(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900",children:(null==l?void 0:l.dataType)==="currency"?h(a.data.max||0):x(a.data.max||0)}),(0,n.jsx)("div",{className:"text-sm text-gray-600",children:"Lớn nhất"})]})]}),"boolean"===a.type&&Array.isArray(a.data)&&(0,n.jsx)("div",{className:"space-y-3",children:a.data.map((e,s)=>{let t=a.data.reduce((e,s)=>e+s.count,0),l=t>0?e.count/t*100:0;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e._id?"bg-green-500":"bg-red-500")}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700",children:e._id?"C\xf3":"Kh\xf4ng"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:x(e.count)}),(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["(",l.toFixed(1),"%)"]})]})]},s)})})]},s)})}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("button",{onClick:g,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"L\xe0m mới thống k\xea"})})]})}var V=t(52056),q=t(52726);function K(e){var s,t,l;let{isOpen:r,onClose:i,onSubmit:c,availableFields:o}=e,[d,g]=(0,a.useState)(""),[h,m]=(0,a.useState)({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),u=e=>{if(e.preventDefault(),!d)return void alert("Vui l\xf2ng chọn trường ng\xe0y");if(h.warningDays&&h.dangerDays&&h.dangerDays>=h.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");let s=o.find(e=>e.name===d);s&&c(s.name,s.isDefault,h)};return r?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,n.jsx)(x.A,{size:20,className:"text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Tạo cấu h\xecnh đếm ngược"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Thiết lập cảnh b\xe1o hết hạn cho trường ng\xe0y (GMT+7)"}),(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded p-2 mt-2",children:(0,n.jsxs)("p",{className:"text-xs text-blue-700",children:["\uD83D\uDCA1 ",(0,n.jsx)("strong",{children:"Lưu \xfd:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, hệ thống sẽ cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn"]})})]})]}),(0,n.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(z.A,{size:20})})]}),(0,n.jsxs)("form",{onSubmit:u,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Chọn trường ng\xe0y ",(0,n.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,n.jsxs)("select",{value:d,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,n.jsx)("option",{value:"",children:"-- Chọn trường ng\xe0y --"}),o.map(e=>(0,n.jsxs)("option",{value:e.name,children:[e.label," ",e.isDefault?"(Cơ bản)":"(T\xf9y chỉnh)"]},e.name))]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(V.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:h.warningDays||30,onChange:e=>m(s=>({...s,warningDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:h.dangerDays||7,onChange:e=>m(s=>({...s,dangerDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:h.enabled,onChange:e=>m(s=>({...s,enabled:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:h.showCountdownBadge,onChange:e=>m(s=>({...s,showCountdownBadge:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Hiển thị đếm ngược chi tiết (ng\xe0y, giờ, ph\xfat, gi\xe2y) theo GMT+7"})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:h.showColorWarning,onChange:e=>m(s=>({...s,showColorWarning:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:h.hideExpired,onChange:e=>m(s=>({...s,hideExpired:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(p.A,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:null==(s=h.emailNotification)?void 0:s.enabled,onChange:e=>m(s=>({...s,emailNotification:{...s.emailNotification,enabled:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),(null==(t=h.emailNotification)?void 0:t.enabled)&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:(null==(l=h.emailNotification)?void 0:l.daysBefore)||7,onChange:e=>m(s=>({...s,emailNotification:{...s.emailNotification,daysBefore:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,n.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,n.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,n.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),h.showCountdownBadge&&(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,n.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:()=>{g(""),m({enabled:!0,warningDays:30,dangerDays:7,showInList:!0,showCountdownBadge:!0,showColorWarning:!0,hideExpired:!1,emailNotification:{enabled:!1,daysBefore:7}}),i()},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsx)("button",{type:"submit",form:"countdown-form",onClick:u,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Tạo cấu h\xecnh"})]})]})}):null}function O(e){var s,t,l;let{isOpen:r,onClose:i,onSubmit:c,countdown:o,getFieldLabel:d}=e,[g,h]=(0,a.useState)({});(0,a.useEffect)(()=>{o&&h(o.countdownConfig)},[o]);let m=e=>{if(e.preventDefault(),o){if(g.warningDays&&g.dangerDays&&g.dangerDays>=g.warningDays)return void alert("Số ng\xe0y nguy hiểm phải nhỏ hơn số ng\xe0y cảnh b\xe1o");c(o._id,g)}};return r&&o?(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,n.jsx)(x.A,{size:20,className:"text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Chỉnh sửa cấu h\xecnh đếm ngược"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:d(o.fieldName)})]})]}),(0,n.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg",children:(0,n.jsx)(z.A,{size:20})})]}),(0,n.jsxs)("form",{onSubmit:m,className:"p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(V.A,{size:16}),"Cấu h\xecnh cảnh b\xe1o"]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y cảnh b\xe1o"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:g.warningDays||30,onChange:e=>h(s=>({...s,warningDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o v\xe0ng khi c\xf2n X ng\xe0y"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Số ng\xe0y nguy hiểm"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:g.dangerDays||7,onChange:e=>h(s=>({...s,dangerDays:parseInt(e.target.value)})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,n.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Hiển thị cảnh b\xe1o đỏ khi c\xf2n X ng\xe0y"})]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"Cấu h\xecnh hiển thị"}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.enabled,onChange:e=>h(s=>({...s,enabled:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Bật đếm ngược"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"K\xedch hoạt t\xednh năng đếm ngược cho trường n\xe0y"})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.showCountdownBadge,onChange:e=>h(s=>({...s,showCountdownBadge:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị badge đếm ngược"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:'Hiển thị badge "C\xf2n X ng\xe0y" trong danh s\xe1ch'})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.showColorWarning,onChange:e=>h(s=>({...s,showColorWarning:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Hiển thị m\xe0u cảnh b\xe1o"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Thay đổi m\xe0u sắc theo trạng th\xe1i (xanh/v\xe0ng/đỏ)"})]})]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.hideExpired,onChange:e=>h(s=>({...s,hideExpired:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Ẩn nếu qu\xe1 hạn"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động ẩn badge khi đ\xe3 qu\xe1 hạn"})]})]})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("h4",{className:"text-md font-medium text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(p.A,{size:16}),"Th\xf4ng b\xe1o email (T\xf9y chọn)"]}),(0,n.jsxs)("label",{className:"flex items-center gap-3",children:[(0,n.jsx)("input",{type:"checkbox",checked:null==(s=g.emailNotification)?void 0:s.enabled,onChange:e=>h(s=>({...s,emailNotification:{...s.emailNotification,enabled:e.target.checked}})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"text-sm font-medium",children:"Gửi email cảnh b\xe1o"}),(0,n.jsx)("p",{className:"text-xs text-gray-500",children:"Tự động gửi email th\xf4ng b\xe1o trước hạn"})]})]}),(null==(t=g.emailNotification)?void 0:t.enabled)&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Gửi email trước (ng\xe0y)"}),(0,n.jsx)("input",{type:"number",min:"1",max:"365",value:(null==(l=g.emailNotification)?void 0:l.daysBefore)||7,onChange:e=>h(s=>({...s,emailNotification:{...s.emailNotification,daysBefore:parseInt(e.target.value)}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,n.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,n.jsx)("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"Xem trước:"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Ng\xe0y:"}),(0,n.jsx)("span",{className:"text-sm",children:"25/12/2024"})]}),g.showCountdownBadge&&(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Badge:"}),(0,n.jsx)("span",{className:"inline-flex items-center gap-1 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 border border-yellow-200 rounded-full",children:"⏰ C\xf2n 15 ng\xe0y"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200",children:[(0,n.jsx)("button",{type:"button",onClick:i,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,n.jsx)("button",{type:"submit",onClick:m,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Cập nhật"})]})]})}):null}var X=t(78874),G=t(42529);function W(e){let{date:s,warningDays:t=30,dangerDays:l=7,showIcon:r=!0,showDetailed:i=!1,size:c="md",className:o=""}=e,[d,g]=(0,a.useState)(null),[h,m]=(0,a.useState)("none");(0,a.useEffect)(()=>{if(!s){g(null),m("none");return}let e=()=>{let e=(()=>{let e=new Date(s),n=new Date(e);n.setDate(n.getDate()+t);let a=new Date,l=new Date(a.getTime()+6e4*a.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),n.setHours(0,0,0,0),l.setHours(0,0,0,0);let r=e.getTime()-l.getTime(),i=n.getTime()-l.getTime();if(i<0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(i/864e5),isExpired:!0};if(r>0)return{days:0,hours:0,minutes:0,seconds:0,totalDays:Math.ceil(i/864e5),isExpired:!1};let c=Math.floor(i/864e5),o=Math.floor(i%864e5/36e5);return{days:c,hours:o,minutes:Math.floor(i%36e5/6e4),seconds:Math.floor(i%6e4/1e3),totalDays:Math.ceil(i/864e5),isExpired:!1}})();if(g(e),e.isExpired)m("expired");else{let t=new Date(s),n=new Date,a=new Date(n.getTime()+6e4*n.getTimezoneOffset()+252e5);t.setHours(0,0,0,0),a.setHours(0,0,0,0),t.getTime()-a.getTime()>0?m("safe"):e.totalDays<=l?m("danger"):m("warning")}};e();let n=setInterval(e,1e3);return()=>clearInterval(n)},[s,t,l]);let u=()=>{if(!d)return"Kh\xf4ng c\xf3 dữ liệu";if(d.isExpired)return"Đ\xe3 hết hạn";let e=new Date(s),t=new Date,n=new Date(t.getTime()+6e4*t.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),n.setHours(0,0,0,0);let a=e.getTime()-n.getTime();if(a>0){let e=Math.ceil(a/864e5);return"C\xf2n ".concat(e," ng\xe0y")}if(1===d.totalDays)return"C\xf2n 1 ng\xe0y";{if(d.totalDays>1)return"C\xf2n ".concat(d.totalDays," ng\xe0y");if(0===d.totalDays)return"Hết hạn h\xf4m nay";let e=Math.abs(d.totalDays);return"Qu\xe1 hạn ".concat(e," ng\xe0y")}};if(!s||!d)return null;let b={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:X.A,color:"text-red-600"},danger:{bg:"bg-red-50",text:"text-red-700",border:"border-red-300",icon:V.A,color:"text-red-500"},warning:{bg:"bg-yellow-50",text:"text-yellow-700",border:"border-yellow-300",icon:V.A,color:"text-yellow-500"},safe:{bg:"bg-green-50",text:"text-green-700",border:"border-green-300",icon:G.A,color:"text-green-500"},none:{bg:"bg-gray-50",text:"text-gray-700",border:"border-gray-300",icon:x.A,color:"text-gray-500"}}[h],p={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-3 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-4 py-2 text-base",icon:16,gap:"gap-2"}}[c],y=b.icon;return(0,n.jsxs)("span",{className:"\n        inline-flex items-center ".concat(p.gap," ").concat(p.container,"\n        ").concat(b.bg," ").concat(b.text," ").concat(b.border,"\n        border rounded-full font-medium\n        ").concat(o,"\n      "),title:"".concat(u()," - Hạn: ").concat(new Date(s).toLocaleDateString("vi-VN",{timeZone:"Asia/Ho_Chi_Minh",year:"numeric",month:"2-digit",day:"2-digit"})," (GMT+7)"),children:[r&&(0,n.jsx)(y,{size:p.icon,className:b.color}),u()]})}let P=[{name:"ngayNhanVanThu",label:"Ng\xe0y nhận văn thư"},{name:"ngayThuLy",label:"Ng\xe0y thụ l\xfd"},{name:"ngayBanHanh",label:"Ng\xe0y ban h\xe0nh"}];function Q(e){let{targetModel:s="CourtCase"}=e,[t,l]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[o,d]=(0,a.useState)([]),[h,m]=(0,a.useState)(!0),[u,b]=(0,a.useState)(null),[p,y]=(0,a.useState)(!1);(0,a.useEffect)(()=>{f()},[s]);let f=async()=>{try{m(!0);let e=localStorage.getItem("sessionToken")||"",[t,n]=await Promise.all([q.A.getDateCountdowns(s,e),A.A.getCustomFields(s,e)]);t.payload.success&&l(t.payload.countdowns),n.payload.success&&i(n.payload.fields);let a=n.payload.success?n.payload.fields.filter(e=>"date"===e.dataType||"datetime"===e.dataType):[],r=[...P.map(e=>({...e,isDefault:!0})),...a.map(e=>({name:e.name,label:e.label,isDefault:!1}))];d(r)}catch(e){console.error("Error fetching data:",e),c.oR.error("Lỗi khi tải dữ liệu")}finally{m(!1)}},j=async(e,t,n)=>{try{let a=localStorage.getItem("sessionToken")||"",l=await q.A.upsertDateCountdown({targetModel:s,fieldName:e,isBuiltIn:t,countdownConfig:n},a);l.payload.success?(c.oR.success("Tạo cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),y(!1),f()):c.oR.error(l.payload.message||"Kh\xf4ng thể tạo cấu h\xecnh đếm ngược")}catch(e){console.error("Error creating countdown:",e),c.oR.error("Lỗi khi tạo cấu h\xecnh đếm ngược")}},w=async(e,n)=>{try{let a=localStorage.getItem("sessionToken")||"",l=t.find(s=>s._id===e);if(!l)return;let r=await q.A.upsertDateCountdown({targetModel:s,fieldName:l.fieldName,isBuiltIn:l.isBuiltIn,countdownConfig:n},a);r.payload.success?(c.oR.success("Cập nhật cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),b(null),f()):c.oR.error(r.payload.message||"Kh\xf4ng thể cập nhật cấu h\xecnh đếm ngược")}catch(e){console.error("Error updating countdown:",e),c.oR.error("Lỗi khi cập nhật cấu h\xecnh đếm ngược")}},C=async e=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a cấu h\xecnh đếm ngược n\xe0y?"))try{let s=localStorage.getItem("sessionToken")||"",t=await q.A.deleteDateCountdown(e,s);t.payload.success?(c.oR.success("X\xf3a cấu h\xecnh đếm ngược th\xe0nh c\xf4ng"),f()):c.oR.error(t.payload.message||"Kh\xf4ng thể x\xf3a cấu h\xecnh đếm ngược")}catch(e){console.error("Error deleting countdown:",e),c.oR.error("Lỗi khi x\xf3a cấu h\xecnh đếm ngược")}},k=e=>{let s=o.find(s=>s.name===e);return s?s.label:e},S=()=>{let e=t.map(e=>e.fieldName);return o.filter(s=>!e.includes(s.name))};return h?(0,n.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 flex items-center gap-2",children:[(0,n.jsx)(x.A,{size:20}),"Cấu h\xecnh đếm ngược ng\xe0y hết hạn"]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Thiết lập cảnh b\xe1o v\xe0 đếm ngược cho c\xe1c trường ng\xe0y th\xe1ng (m\xfai giờ GMT+7)"}),(0,n.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-3",children:(0,n.jsxs)("div",{className:"flex items-start gap-2",children:[(0,n.jsx)("span",{className:"text-yellow-600 mt-0.5",children:"\uD83D\uDCA1"}),(0,n.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,n.jsx)("strong",{children:"C\xe1ch hoạt động:"})," Ng\xe0y được chọn l\xe0 ng\xe0y bắt đầu đếm ngược, cộng số ng\xe0y cảnh b\xe1o để t\xednh ng\xe0y hết hạn.",(0,n.jsx)("br",{}),(0,n.jsx)("strong",{children:"V\xed dụ:"})," Ng\xe0y bắt đầu 31/07/2025 + 90 ng\xe0y cảnh b\xe1o = Hết hạn 29/10/2025"]})]})}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"\uD83C\uDF0F GMT+7 (Việt Nam)"}),(0,n.jsx)("span",{className:"text-xs text-gray-500",children:"Đếm ngược theo thời gian thực với độ ch\xednh x\xe1c đến gi\xe2y"})]})]}),(0,n.jsxs)("button",{onClick:()=>y(!0),disabled:0===S().length,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,n.jsx)(v.A,{size:16}),"Th\xeam cấu h\xecnh"]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,n.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,n.jsx)(g.A,{size:20,className:"text-blue-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-900",children:o.length}),(0,n.jsx)("div",{className:"text-sm text-blue-700",children:"Trường ng\xe0y"})]})]})}),(0,n.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,n.jsx)(x.A,{size:20,className:"text-green-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-900",children:t.filter(e=>e.countdownConfig.enabled).length}),(0,n.jsx)("div",{className:"text-sm text-green-700",children:"Đang hoạt động"})]})]})}),(0,n.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,n.jsx)(V.A,{size:20,className:"text-yellow-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-yellow-900",children:t.filter(e=>e.countdownConfig.emailNotification.enabled).length}),(0,n.jsx)("div",{className:"text-sm text-yellow-700",children:"Email cảnh b\xe1o"})]})]})}),(0,n.jsx)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 bg-purple-100 rounded-lg",children:(0,n.jsx)(N.A,{size:20,className:"text-purple-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-purple-900",children:t.length}),(0,n.jsx)("div",{className:"text-sm text-purple-700",children:"Tổng cấu h\xecnh"})]})]})})]}),0===t.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(x.A,{size:48,className:"mx-auto text-gray-400 mb-4"}),(0,n.jsx)("p",{className:"text-gray-500 mb-4",children:"Chưa c\xf3 cấu h\xecnh đếm ngược n\xe0o"}),(0,n.jsx)("button",{onClick:()=>y(!0),disabled:0===S().length,className:"text-blue-600 hover:text-blue-700 font-medium disabled:text-gray-400",children:0===S().length?"Kh\xf4ng c\xf3 trường ng\xe0y n\xe0o để cấu h\xecnh":"Tạo cấu h\xecnh đầu ti\xean"})]}):(0,n.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,n.jsx)("div",{className:"p-4 border rounded-lg ".concat(e.countdownConfig.enabled?"border-green-200 bg-green-50":"border-gray-200 bg-gray-50"),children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"p-2 rounded-lg ".concat(e.countdownConfig.enabled?"bg-green-100":"bg-gray-100"),children:(0,n.jsx)(x.A,{size:16,className:e.countdownConfig.enabled?"text-green-600":"text-gray-600"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900",children:k(e.fieldName)}),(0,n.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 mt-1",children:[(0,n.jsxs)("span",{children:["Cảnh b\xe1o: ",e.countdownConfig.warningDays," ng\xe0y"]}),(0,n.jsxs)("span",{children:["Nguy hiểm: ",e.countdownConfig.dangerDays," ng\xe0y"]}),e.countdownConfig.emailNotification.enabled&&(0,n.jsx)("span",{className:"text-blue-600",children:"\uD83D\uDCE7 Email"})]}),e.countdownConfig.enabled&&(0,n.jsxs)("div",{className:"mt-2",children:[(0,n.jsx)("span",{className:"text-xs text-gray-500 mr-2",children:"Preview (đang đếm ngược):"}),(0,n.jsx)(W,{date:new Date(Date.now()+24*e.countdownConfig.dangerDays*36e5),warningDays:e.countdownConfig.warningDays,dangerDays:e.countdownConfig.dangerDays,size:"sm",showIcon:!0,showDetailed:!0}),(0,n.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(Ng\xe0y bắt đầu + ",e.countdownConfig.warningDays," ng\xe0y = Ng\xe0y hết hạn)"]})]})]})]})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(e.countdownConfig.enabled?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"),children:e.countdownConfig.enabled?"Hoạt động":"Tắt"}),(0,n.jsx)("button",{onClick:()=>b(e),className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,n.jsx)(T.A,{size:16})}),(0,n.jsx)("button",{onClick:()=>C(e._id),className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,n.jsx)(D.A,{size:16})})]})]})},e._id))}),(0,n.jsx)(K,{isOpen:p,onClose:()=>y(!1),onSubmit:j,availableFields:S()}),(0,n.jsx)(O,{isOpen:!!u,onClose:()=>b(null),onSubmit:w,countdown:u,getFieldLabel:k})]})}var Z=t(48203);function J(){let e=(0,i.useRouter)(),[s,t]=(0,a.useState)("fields"),[c,o]=(0,a.useState)([]);return(0,n.jsx)(Z.default,{requiredPermission:"custom_fields_view",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,n.jsx)("button",{onClick:()=>e.back(),className:"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,n.jsx)(l.A,{size:20})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Quản l\xfd trường t\xf9y chỉnh - Vụ việc t\xf2a \xe1n"}),(0,n.jsx)("p",{className:"text-gray-600 mt-1",children:"Tạo v\xe0 quản l\xfd c\xe1c trường dữ liệu t\xf9y chỉnh cho vụ việc t\xf2a \xe1n, giống như Excel"})]})]}),(0,n.jsxs)("div",{className:"flex space-x-1 mb-6",children:[(0,n.jsx)("button",{onClick:()=>t("fields"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("fields"===s?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:"Quản l\xfd trường"}),(0,n.jsxs)("button",{onClick:()=>t("stats"),className:"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ".concat("stats"===s?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:[(0,n.jsx)(r.A,{size:16}),"Thống k\xea"]}),(0,n.jsx)("button",{onClick:()=>t("countdown"),className:"flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ".concat("countdown"===s?"bg-blue-600 text-white":"text-gray-600 hover:bg-gray-100"),children:"⏰ Đếm ngược"})]}),"fields"===s?(0,n.jsx)(B,{targetModel:"CourtCase",onFieldsChange:e=>{o(e)}}):"stats"===s?(0,n.jsx)(M,{targetModel:"CourtCase",fields:c}):(0,n.jsx)(Q,{targetModel:"CourtCase"}),(0,n.jsxs)("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"\uD83D\uDCA1 T\xednh năng giống Excel"}),(0,n.jsxs)("div",{className:"text-blue-800 space-y-2",children:[(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"Tự động tạo cột:"})," Th\xeam trường mới sẽ tự động tạo cột trong bảng danh s\xe1ch"]}),(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"Nhiều kiểu dữ liệu:"})," Văn bản, số, ng\xe0y th\xe1ng, lựa chọn, tiền tệ, v.v."]}),(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"Thống k\xea tự động:"})," Hệ thống tự động t\xednh to\xe1n thống k\xea cho c\xe1c trường mới"]}),(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"K\xe9o thả sắp xếp:"})," Thay đổi thứ tự hiển thị cột bằng c\xe1ch k\xe9o thả"]}),(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"T\xf9y chỉnh hiển thị:"})," Ẩn/hiện cột, điều chỉnh độ rộng"]}),(0,n.jsxs)("p",{children:["• ",(0,n.jsx)("strong",{children:"Validation tự động:"})," Kiểm tra dữ liệu theo quy tắc đ\xe3 định"]})]})]})]})})}}},e=>{e.O(0,[9268,2739,4744,6031,297,8441,1255,7358],()=>e(e.s=30486)),_N_E=e.O()}]);