(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2828],{11010:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},20929:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var a=r(95155),s=r(12115),n=r(74744),l=r(25656);let i={getCourtCases:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&""!==a&&t.append(r,a.toString())});let r=t.toString();return l.Ay.get(r?"/api/court-cases?".concat(r):"/api/court-cases")},createCourtCase:e=>l.Ay.post("/api/court-cases",e),updateCourtCase:(e,t)=>l.Ay.put("/api/court-cases/".concat(e),t),deleteCourtCase:e=>l.Ay.delete("/api/court-cases/".concat(e)),bulkDeleteCourtCases:e=>l.Ay.post("/api/court-cases/bulk-delete",{ids:e}),downloadTemplate:async()=>{let e={};{let t=localStorage.getItem("sessionToken");t&&(e.Authorization="Bearer ".concat(t))}let t=await fetch("/api/court-cases/template",{method:"GET",headers:e});if(!t.ok)throw Error("Failed to download template");let r=await t.blob(),a=window.URL.createObjectURL(r),s=document.createElement("a");return s.href=a,s.download="mau-import-vu-viec-toa-an.xlsx",document.body.appendChild(s),s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(a),{success:!0}},exportCourtCases:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[r,a]=e;null!=a&&""!==a&&t.append(r,a.toString())});let r=t.toString(),a={};{let e=localStorage.getItem("sessionToken");e&&(a.Authorization="Bearer ".concat(e))}let s=await fetch(r?"/api/court-cases/export?".concat(r):"/api/court-cases/export",{method:"GET",headers:a});if(!s.ok)throw Error("Failed to export data");return s.arrayBuffer()},previewImport:e=>{let t=new FormData;return t.append("file",e),l.Ay.post("/api/court-cases/preview-import",t)},importCourtCases:e=>{let t=new FormData;return t.append("file",e),l.Ay.post("/api/court-cases/import",t)}};var c=r(57828),o=r(11010),d=r(71360),u=r(97849),m=r(75444),g=r(57052),h=r(84961),x=r(52726),p=r(78874),y=r(52056),f=r(26983),b=r(42529);let v=e=>{if(!("string"==typeof e&&e.match(/^\d{2}\/\d{2}\/\d{4}$/)))return new Date(e);{let[t,r,a]=e.split("/");return new Date(parseInt(a),parseInt(r)-1,parseInt(t))}};function j(e){let{date:t,warningDays:r=30,dangerDays:n=7,showIcon:l=!0,showText:i=!0,size:c="md",className:o=""}=e,[d,u]=(0,s.useState)(null),[m,g]=(0,s.useState)("none");(0,s.useEffect)(()=>{if(!t){u(null),g("none");return}let e=(()=>{let e=v(t);if(isNaN(e.getTime()))return null;let a=new Date(e);a.setDate(a.getDate()+r);let s=new Date,n=new Date(s.getTime()+6e4*s.getTimezoneOffset()+252e5);return e.setHours(0,0,0,0),a.setHours(0,0,0,0),n.setHours(0,0,0,0),Math.ceil((a.getTime()-n.getTime())/864e5)})();if(u(e),e<0)g("expired");else{let r=new Date(t),a=new Date,s=new Date(a.getTime()+6e4*a.getTimezoneOffset()+252e5);r.setHours(0,0,0,0),s.setHours(0,0,0,0),r.getTime()-s.getTime()>0?g("safe"):e<=n?g("danger"):g("warning")}},[t,r,n]);let h=()=>{if(null===d)return"";if(d<0){let e=Math.abs(d);return"Qu\xe1 hạn ".concat(e," ng\xe0y")}let e=v(t),r=new Date,a=new Date(r.getTime()+6e4*r.getTimezoneOffset()+252e5);e.setHours(0,0,0,0),a.setHours(0,0,0,0);let s=e.getTime()-a.getTime();if(s>0){let e=Math.ceil(s/864e5);return"C\xf2n ".concat(e," ng\xe0y")}return 0===d?"Hết hạn h\xf4m nay":1===d?"C\xf2n 1 ng\xe0y":"C\xf2n ".concat(d," ng\xe0y")};if(!t||null===d)return null;let x={expired:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:p.A,color:"text-red-600"},danger:{bg:"bg-red-100",text:"text-red-800",border:"border-red-200",icon:y.A,color:"text-red-600"},warning:{bg:"bg-yellow-100",text:"text-yellow-800",border:"border-yellow-200",icon:f.A,color:"text-yellow-600"},safe:{bg:"bg-green-100",text:"text-green-800",border:"border-green-200",icon:b.A,color:"text-green-600"},none:{bg:"bg-gray-100",text:"text-gray-800",border:"border-gray-200",icon:f.A,color:"text-gray-600"}}[m],j={sm:{container:"px-2 py-1 text-xs",icon:12,gap:"gap-1"},md:{container:"px-2.5 py-1.5 text-sm",icon:14,gap:"gap-1.5"},lg:{container:"px-3 py-2 text-base",icon:16,gap:"gap-2"}}[c],N=x.icon;return(0,a.jsxs)("span",{className:"\n        inline-flex items-center ".concat(j.gap," ").concat(j.container,"\n        ").concat(x.bg," ").concat(x.text," ").concat(x.border,"\n        border rounded-full font-medium\n        ").concat(o,"\n      "),title:"".concat(h()," - ").concat(new Date(t).toLocaleDateString("vi-VN")),children:[l&&(0,a.jsx)(N,{size:j.icon,className:x.color}),i&&h()]})}let N=e=>{var t;let{cases:r,onCaseSelect:l,onCaseEdit:i,onCaseDelete:p,onBulkAction:y,onSort:f,currentSort:b,loading:v=!1}=e,{hasPermission:N}=(0,m.S)(),[w,C]=(0,s.useState)([]),[k,D]=(0,s.useState)(!1),[S,T]=(0,s.useState)([]),[A,E]=(0,s.useState)([]),[R,B]=(0,s.useState)(null),[F,_]=(0,s.useState)([]);(0,s.useEffect)(()=>{L();let e=setInterval(()=>{L()},3e4);return()=>clearInterval(e)},[]);let L=async()=>{try{let e=localStorage.getItem("sessionToken")||"",[t,r,a]=await Promise.all([g.A.getCustomFields("CourtCase",e),h.A.getFieldConfiguration("CourtCase",e),x.A.getDateCountdowns("CourtCase",e)]);if(t.payload.success&&r.payload.success){T(t.payload.fields),B(r.payload.configuration),a.payload.success&&_(a.payload.countdowns);let e=[];t.payload.fields.forEach(t=>{t.config.showInList&&e.push({...t})}),e.sort((e,t)=>e.config.sortOrder-t.config.sortOrder),E(e)}}catch(e){console.error("Error fetching custom fields:",e)}},z=e=>{let{field:t,children:r}=e,s=(null==b?void 0:b.sortBy)===t,n=s&&(null==b?void 0:b.sortOrder)==="asc",l=s&&(null==b?void 0:b.sortOrder)==="desc";return(0,a.jsxs)("button",{onClick:()=>(e=>{if(!f)return;let t="asc";(null==b?void 0:b.sortBy)===e&&(t="asc"===b.sortOrder?"desc":"asc"),f(e,t)})(t),className:"flex items-center gap-1 hover:text-gray-700 transition-colors",children:[r,(0,a.jsxs)("span",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-xs leading-none ".concat(n?"text-blue-600":"text-gray-400"),children:"▲"}),(0,a.jsx)("span",{className:"text-xs leading-none ".concat(l?"text-blue-600":"text-gray-400"),children:"▼"})]})]})},M=(e,t)=>{var r,a;let s=null==(r=e.customFields)?void 0:r[t.name];if(!s)return"";switch(t.dataType){case"date":return s?(0,u.Yq)(s):"";case"datetime":if(!s)return"";let n=new Date(s);if(isNaN(n.getTime()))return"";let l=n.getDate().toString().padStart(2,"0"),i=(n.getMonth()+1).toString().padStart(2,"0"),c=n.getFullYear().toString(),o=n.getHours().toString().padStart(2,"0"),d=n.getMinutes().toString().padStart(2,"0");return"".concat(l,"/").concat(i,"/").concat(c," ").concat(o,":").concat(d);case"currency":return new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(s);case"percentage":return"".concat(s,"%");case"boolean":return s?"C\xf3":"Kh\xf4ng";case"select":let m=null==(a=t.config.options)?void 0:a.find(e=>e.value===s);return m?m.label:s;case"multiselect":if(Array.isArray(s))return s.map(e=>{var r;let a=null==(r=t.config.options)?void 0:r.find(t=>t.value===e);return a?a.label:e}).join(", ");return s;default:return s.toString()}};return v?(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsx)("div",{className:"space-y-4",children:[...Array(8)].map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},t))})})}):(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100",children:[(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Danh s\xe1ch vụ việc t\xf2a \xe1n"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Hiển thị ",(null==r?void 0:r.length)||0," vụ việc với ",(null==A||null==(t=A.filter(e=>{var t;return null==e||null==(t=e.config)?void 0:t.showInList}))?void 0:t.length)||0," cột • STT & Số Hồ Sơ tự động"]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Tổng: ",(0,a.jsx)("span",{className:"font-semibold text-gray-900",children:(null==r?void 0:r.length)||0})," vụ việc"]})})]})}),w.length>0&&(0,a.jsx)("div",{className:"bg-blue-50 border-b border-blue-200 px-6 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("span",{className:"text-sm text-blue-700 font-medium",children:["Đ\xe3 chọn ",w.length," vụ việc"]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{if(0===w.length)return void n.oR.warning("Vui l\xf2ng chọn \xedt nhất một vụ việc để x\xf3a");confirm("Bạn c\xf3 chắc chắn muốn x\xf3a ".concat(w.length," vụ việc đ\xe3 chọn?"))&&(y(w,"delete"),C([]),D(!1))},className:"flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium",children:"X\xf3a đ\xe3 chọn"}),(0,a.jsx)("button",{onClick:()=>{C([]),D(!1)},className:"flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors",children:"Bỏ chọn"})]})]})}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[N("court_case_delete")&&(0,a.jsx)("th",{className:"px-4 py-4 text-left w-12",children:(0,a.jsx)("input",{type:"checkbox",checked:k,onChange:e=>(e=>{D(e),e?C(r.map(e=>e._id)):C([])})(e.target.checked),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,a.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-16",children:f?(0,a.jsx)(z,{field:"createdAt",children:"STT"}):"STT"}),(0,a.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:f?(0,a.jsx)(z,{field:"soHoSo",children:"SỐ HỒ SƠ"}):"SỐ HỒ SƠ"}),A.filter(e=>e.config.showInList).map(e=>(0,a.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider ".concat(e.width||"w-32"),style:{width:e.config.columnWidth||150},children:f?(0,a.jsx)(z,{field:e.name,children:e.label}):e.label},e._id)),(0,a.jsx)("th",{className:"px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32",children:"THAO T\xc1C"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:r.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors cursor-pointer",onClick:()=>l(e),children:[N("court_case_delete")&&(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,a.jsx)("input",{type:"checkbox",checked:w.includes(e._id),onChange:t=>{t.stopPropagation(),((e,t)=>{t?C(t=>[...t,e]):(C(t=>t.filter(t=>t!==e)),D(!1))})(e._id,t.target.checked)},className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"})}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsx)("span",{className:"font-semibold",children:e.stt||t+1})}),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsx)("span",{className:"font-mono",children:e.soHoSo||"Chưa c\xf3"})}),A.filter(e=>e.config.showInList).map(t=>(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-900",style:{maxWidth:t.config.columnWidth||150},children:"select"===t.dataType?(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ".concat((e=>{switch(e){case"Chưa giải quyết":return"bg-red-100 text-red-800 border-red-200";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800 border-green-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(M(e,t))),children:M(e,t)}):(e=>"date"===e.dataType||"datetime"===e.dataType)(t)?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"truncate",title:M(e,t),children:M(e,t)}),(()=>{let r,s=(r=t.name,F.find(e=>e.fieldName===r&&e.countdownConfig.enabled));if(s&&s.countdownConfig.showCountdownBadge){let r=M(e,t);if(r&&(e=>{if(!e||""===e.trim())return!1;if(e.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[t,r,a]=e.split("/"),s=new Date(parseInt(a),parseInt(r)-1,parseInt(t));return!isNaN(s.getTime())&&s.getDate()===parseInt(t)&&s.getMonth()===parseInt(r)-1&&s.getFullYear()===parseInt(a)}return!isNaN(new Date(e).getTime())})(r))return(0,a.jsx)(j,{date:r,warningDays:s.countdownConfig.warningDays,dangerDays:s.countdownConfig.dangerDays,size:"sm",showIcon:!0,showText:!0})}return null})()]}):(0,a.jsx)("div",{className:"truncate",title:M(e,t),children:M(e,t)})},t._id)),(0,a.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),l(e)},className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Xem chi tiết",children:(0,a.jsx)(c.A,{size:16})}),N("court_case_edit")&&(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),i(e)},className:"p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",title:"Chỉnh sửa",children:(0,a.jsx)(o.A,{size:16})}),N("court_case_delete")&&(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),p(e._id)},className:"p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors",title:"X\xf3a",children:(0,a.jsx)(d.A,{size:16})})]})})]},e._id))})]})}),0===r.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 text-6xl mb-4",children:"\uD83D\uDCCB"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Kh\xf4ng c\xf3 vụ việc n\xe0o"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Chưa c\xf3 dữ liệu vụ việc t\xf2a \xe1n để hiển thị."})]})]})},w=e=>{let{id:t,value:r="",onChange:n,className:l="",placeholder:i="dd/mm/yyyy",required:c=!1,min:o,max:d,disabled:u=!1}=e,[m,g]=(0,s.useState)(r),[h,x]=(0,s.useState)(!1),[p,y]=(0,s.useState)(null),f=(0,s.useRef)(null),b=(0,s.useRef)(null),v=e=>{if(!e||!e.match(/^\d{2}\/\d{2}\/\d{4}$/))return null;let[t,r,a]=e.split("/").map(Number),s=new Date(a,r-1,t);return s.getFullYear()===a&&s.getMonth()===r-1&&s.getDate()===t?s:null};(0,s.useEffect)(()=>{r&&(y(v(r)),g(r))},[r]);let j=e=>{let t=(e=>{let t=e.getDate().toString().padStart(2,"0"),r=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getFullYear();return"".concat(t,"/").concat(r,"/").concat(a)})(e);y(e),g(t),null==n||n(t),x(!1)},[N,w]=(0,s.useState)(()=>p||new Date),C=(e=>{let t=new Date(e.getFullYear(),e.getMonth(),1),r=new Date(t);r.setDate(r.getDate()-t.getDay());let a=[],s=new Date(r);for(let e=0;e<42;e++)a.push(new Date(s)),s.setDate(s.getDate()+1);return a})(N);return(0,s.useEffect)(()=>{let e=e=>{b.current&&!b.current.contains(e.target)&&x(!1)};return h&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[h]),(0,a.jsxs)("div",{className:"relative",ref:b,children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{ref:f,id:t,type:"text",value:m,onChange:e=>{let t=e.target.value.replace(/\D/g,"");if(t.length>=2&&(t=t.substring(0,2)+"/"+t.substring(2)),t.length>=5&&(t=t.substring(0,5)+"/"+t.substring(5,9)),g(t),10===t.length){let e=v(t);e&&(y(e),null==n||n(t))}else 0===t.length&&(y(null),null==n||n(""))},className:"w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ".concat(l),placeholder:i,required:c,disabled:u,maxLength:10}),(0,a.jsx)("button",{type:"button",onClick:()=>!u&&x(!h),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 disabled:opacity-50",disabled:u,children:"\uD83D\uDCC5"})]}),h&&!u&&(0,a.jsxs)("div",{className:"absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 p-4 min-w-[280px]",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("button",{type:"button",onClick:()=>w(new Date(N.getFullYear(),N.getMonth()-1)),className:"p-1 hover:bg-gray-100 rounded",children:"◀"}),(0,a.jsxs)("div",{className:"font-medium",children:[["Th\xe1ng 1","Th\xe1ng 2","Th\xe1ng 3","Th\xe1ng 4","Th\xe1ng 5","Th\xe1ng 6","Th\xe1ng 7","Th\xe1ng 8","Th\xe1ng 9","Th\xe1ng 10","Th\xe1ng 11","Th\xe1ng 12"][N.getMonth()]," ",N.getFullYear()]}),(0,a.jsx)("button",{type:"button",onClick:()=>w(new Date(N.getFullYear(),N.getMonth()+1)),className:"p-1 hover:bg-gray-100 rounded",children:"▶"})]}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1 mb-2",children:["CN","T2","T3","T4","T5","T6","T7"].map(e=>(0,a.jsx)("div",{className:"text-center text-xs font-medium text-gray-500 p-2",children:e},e))}),(0,a.jsx)("div",{className:"grid grid-cols-7 gap-1",children:C.map((e,t)=>{let r=e.getMonth()===N.getMonth(),s=p&&e.getDate()===p.getDate()&&e.getMonth()===p.getMonth()&&e.getFullYear()===p.getFullYear(),n=e.getDate()===new Date().getDate()&&e.getMonth()===new Date().getMonth()&&e.getFullYear()===new Date().getFullYear();return(0,a.jsx)("button",{type:"button",onClick:()=>j(e),className:"\n                    p-2 text-sm rounded hover:bg-blue-100 transition-colors\n                    ".concat(!r?"text-gray-300":"text-gray-700","\n                    ").concat(s?"bg-blue-500 text-white hover:bg-blue-600":"","\n                    ").concat(n&&!s?"bg-blue-50 text-blue-600 font-medium":"","\n                  "),children:e.getDate()},t)})}),(0,a.jsx)("div",{className:"mt-4 pt-3 border-t border-gray-200",children:(0,a.jsx)("button",{type:"button",onClick:()=>j(new Date),className:"w-full px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors",children:"H\xf4m nay"})})]})]})},C=e=>{let{courtCase:t,onSubmit:r,onCancel:l,loading:i=!1}=e,c=!!t,[o,d]=(0,s.useState)([]),[u,m]=(0,s.useState)({});(0,s.useEffect)(()=>{h()},[]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&l()};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}},[l]),(0,s.useEffect)(()=>{t&&t.customFields&&m({...t.customFields})},[t]);let h=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await g.A.getCustomFields("CourtCase",e);t.payload.success&&d(t.payload.fields)}catch(e){console.error("Error fetching custom fields:",e)}},x=(e,t)=>{m(r=>({...r,[e]:t}))};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4",onClick:e=>{e.target===e.currentTarget&&l()},children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden relative",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:c?"✏️":"➕"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:c?"Chỉnh sửa vụ việc":"Th\xeam vụ việc mới"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:c?"Cập nhật vụ việc ".concat((null==t?void 0:t.soHoSo)||""," (").concat(o.length," trường t\xf9y chỉnh)"):"Tạo vụ việc mới với STT & Số Hồ Sơ tự động (".concat(o.length," trường t\xf9y chỉnh)")})]})]}),(0,a.jsx)("button",{onClick:l,disabled:i,className:"text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50",children:"✕"})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]",children:(0,a.jsx)("form",{id:"court-case-form",onSubmit:e=>{e.preventDefault();let t=[];if(o.forEach(e=>{e.config.required&&!u[e.name]&&t.push("".concat(e.label," l\xe0 bắt buộc"))}),t.length>0)return void t.forEach(e=>n.oR.error(e));r({customFields:u})},className:"p-6 space-y-8",children:o.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2",children:"⚙️ Th\xf4ng tin bổ sung"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:o.map(e=>(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"custom_".concat(e.name),className:"block text-sm font-medium text-gray-700 mb-2",children:[e.label,e.config.required&&(0,a.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(e=>{var t,r;let s=u[e.name]||e.config.defaultValue||"",n="custom_".concat(e.name);switch(e.dataType){case"text":case"email":case"phone":case"url":return(0,a.jsx)("input",{id:n,type:"email"===e.dataType?"email":"phone"===e.dataType?"tel":"url"===e.dataType?"url":"text",value:s,onChange:t=>x(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,maxLength:e.config.maxLength,minLength:e.config.minLength,pattern:e.config.pattern});case"textarea":return(0,a.jsx)("textarea",{id:n,value:s,onChange:t=>x(e.name,t.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,maxLength:e.config.maxLength,minLength:e.config.minLength});case"number":case"currency":case"percentage":return(0,a.jsx)("input",{id:n,type:"number",value:s,onChange:t=>x(e.name,parseFloat(t.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required,min:e.config.min,max:e.config.max,step:e.config.decimals?"0.".concat("0".repeat(e.config.decimals-1),"1"):"1"});case"date":return(0,a.jsx)(w,{id:n,value:s,onChange:t=>x(e.name,t),className:"focus:ring-blue-500",placeholder:"dd/mm/yyyy",required:e.config.required});case"datetime":return(0,a.jsx)("input",{id:n,type:"datetime-local",value:s?new Date(s).toISOString().slice(0,16):"",onChange:t=>x(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:e.config.required});case"boolean":return(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"radio",name:n,checked:!0===s,onChange:()=>x(e.name,!0),className:"text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{children:"C\xf3"})]}),(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"radio",name:n,checked:!1===s,onChange:()=>x(e.name,!1),className:"text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{children:"Kh\xf4ng"})]})]});case"select":return(0,a.jsxs)("select",{id:n,value:s,onChange:t=>x(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:e.config.required,children:[(0,a.jsxs)("option",{value:"",children:["-- Chọn ",e.label.toLowerCase()," --"]}),null==(t=e.config.options)?void 0:t.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]});case"multiselect":return(0,a.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2",children:null==(r=e.config.options)?void 0:r.map(t=>(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:Array.isArray(s)&&s.includes(t.value),onChange:r=>{let a=Array.isArray(s)?s:[];r.target.checked?x(e.name,[...a,t.value]):x(e.name,a.filter(e=>e!==t.value))},className:"text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm",children:t.label})]},t.value))});default:return(0,a.jsx)("input",{id:n,type:"text",value:s,onChange:t=>x(e.name,t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Nhập ".concat(e.label.toLowerCase()),required:e.config.required})}})(e),e.description&&(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:e.description})]},e._id))})]})})}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 p-6 border-t border-gray-200 bg-gray-50",children:[(0,a.jsx)("button",{type:"button",onClick:l,className:"px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors",children:"Hủy"}),(0,a.jsx)("button",{type:"submit",form:"court-case-form",disabled:i,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:i?"Đang xử l\xfd...":c?"Cập nhật":"Tạo mới"})]}),i&&(0,a.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10",children:(0,a.jsxs)("div",{className:"flex flex-col items-center gap-3",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:c?"Đang cập nhật...":"Đang tạo vụ việc..."})]})})]})})},k=e=>{let{courtCase:t,onClose:r,onEdit:l,onDelete:i}=e,{hasPermission:c}=(0,m.S)(),[p,y]=(0,s.useState)([]),[f,b]=(0,s.useState)([]),[v,N]=(0,s.useState)(!0);(0,s.useEffect)(()=>{w()},[]);let w=async()=>{try{N(!0);let e=localStorage.getItem("sessionToken")||"",[t,r,a]=await Promise.all([g.A.getCustomFields("CourtCase",e),h.A.getFieldConfiguration("CourtCase",e),x.A.getDateCountdowns("CourtCase",e)]);if(t.payload.success&&r.payload.success){let e=[];t.payload.fields.forEach(t=>{e.push({...t})}),e.sort((e,t)=>(e.config.sortOrder||0)-(t.config.sortOrder||0)),y(e)}a.payload.success&&b(a.payload.countdowns)}catch(e){console.error("Error fetching fields and countdowns:",e),n.oR.error("C\xf3 lỗi xảy ra khi tải th\xf4ng tin trường")}finally{N(!1)}};return(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,a.jsxs)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-3xl mr-3",children:"\uD83D\uDCCB"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"Chi tiết vụ việc"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Th\xf4ng tin chi tiết vụ việc t\xf2a \xe1n"})]})]}),(0,a.jsx)("button",{onClick:r,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),v?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Đang tải th\xf4ng tin..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",style:{color:"#111827"},children:"Th\xf4ng tin hệ thống"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:["STT",(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded",children:"Tự động"})]}),(0,a.jsx)("div",{className:"text-lg font-semibold",style:{color:"#111827"},children:t.stt})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:["Số Hồ Sơ",(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded",children:"XX/DD/MM/YYYY"})]}),(0,a.jsx)("div",{className:"text-lg font-mono font-semibold",style:{color:"#111827"},children:t.soHoSo||"Chưa c\xf3"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"\uD83D\uDCCB Th\xf4ng tin chi tiết"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.filter(e=>!1!==e.config.showInDetail).map(e=>{var r;let s,n=(e=>{var r;return(null==(r=t.customFields)?void 0:r[e.name])||""})(e),l=null==(r=t.customFields)?void 0:r[e.name],i=(e=>"date"===e.dataType||"datetime"===e.dataType)(e)?(s=e.name,f.find(e=>e.fieldName===s&&e.countdownConfig.enabled&&e.countdownConfig.showCountdownBadge)):null;return(0,a.jsxs)("div",{className:"textarea"===e.dataType?"md:col-span-2":"",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-600 mb-1",children:[e.label,(0,a.jsx)("span",{className:"ml-2 text-xs px-2 py-0.5 bg-green-100 text-green-600 rounded",children:"T\xf9y chỉnh"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"flex-1",style:{color:"#111827"},children:((e,t)=>{if(!t&&0!==t)return(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 dữ liệu"});switch(e.dataType){case"select":if("trangThaiGiaiQuyet"===e.name)return(0,a.jsx)("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full ".concat((e=>{switch(e){case"Đ\xe3 giải quyết":return"bg-green-100 text-green-800";case"Đang giải quyết":return"bg-yellow-100 text-yellow-800";case"Chưa giải quyết":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(t)),children:t});return(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:t});case"textarea":return(0,a.jsx)("div",{className:"whitespace-pre-wrap",children:t});case"number":return(0,a.jsx)("span",{className:"font-mono",children:t.toLocaleString()});case"date":return(0,a.jsx)("span",{children:(0,u.Yq)(t)});case"datetime":if(!t)return(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Chưa c\xf3 dữ liệu"});let r=new Date(t);if(isNaN(r.getTime()))return(0,a.jsx)("span",{className:"text-gray-400 italic",children:"Ng\xe0y kh\xf4ng hợp lệ"});let s=r.getDate().toString().padStart(2,"0"),n=(r.getMonth()+1).toString().padStart(2,"0"),l=r.getFullYear().toString(),i=r.getHours().toString().padStart(2,"0"),c=r.getMinutes().toString().padStart(2,"0");return(0,a.jsx)("span",{children:"".concat(s,"/").concat(n,"/").concat(l," ").concat(i,":").concat(c)});case"boolean":return t?"✅ C\xf3":"❌ Kh\xf4ng";default:return(0,a.jsx)("span",{children:t})}})(e,n)}),i&&l&&(0,a.jsx)(j,{date:l,warningDays:i.countdownConfig.warningDays,dangerDays:i.countdownConfig.dangerDays,size:"sm",showIcon:!0,showText:!0})]})]},e._id)})})]}),(0,a.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 flex items-center",style:{color:"#111827"},children:"ℹ️ Th\xf4ng tin hệ thống"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[t.createdBy&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người tạo"}),(0,a.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:t.createdBy.username})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y tạo"}),(0,a.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,u.Yq)(t.createdAt)})]})]}),t.updatedBy&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Người cập nhật cuối"}),(0,a.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:t.updatedBy.username})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-600",children:"Ng\xe0y cập nhật cuối"}),(0,a.jsx)("p",{className:"text-lg",style:{color:"#111827"},children:(0,u.Yq)(t.updatedAt)})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 pt-6 border-t mt-6",children:[(0,a.jsx)("button",{onClick:r,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},children:"Đ\xf3ng"}),c("court_case_edit")&&(0,a.jsxs)("button",{onClick:()=>{r(),l(t)},className:"flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#059669",color:"#ffffff"},children:[(0,a.jsx)(o.A,{size:16}),"Chỉnh sửa"]}),c("court_case_delete")&&(0,a.jsxs)("button",{onClick:()=>{confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?")&&(i(t._id),r())},className:"flex items-center gap-2 px-6 py-2 rounded-md hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:[(0,a.jsx)(d.A,{size:16}),"X\xf3a"]})]})]})})};var D=r(46673),S=r(83101),T=r(64269);let A=(0,S.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),E=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:l=!1,...i}=e,c=l?D.DX:"button";return(0,a.jsx)(c,{className:(0,T.cn)(A({variant:s,size:n,className:r})),ref:t,...i})});E.displayName="Button";var R=r(47972),B=r(52799),F=r(65229),_=r(21786);let L=e=>{let{preview:t,onConfirmImport:r,onCancel:n,isImporting:l}=e,[i,o]=(0,s.useState)(!1),[d,u]=(0,s.useState)("all"),m="all"===d?t.data:t.data.filter(e=>e.status===d);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Xem trước dữ liệu import"}),(0,a.jsxs)(E,{variant:"outline",onClick:n,className:"text-gray-600 hover:text-gray-800",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Đ\xf3ng"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(R.Zp,{children:[(0,a.jsx)(R.aR,{className:"pb-2",children:(0,a.jsx)(R.ZB,{className:"text-sm font-medium text-gray-600",children:"Tổng số d\xf2ng"})}),(0,a.jsx)(R.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.summary.totalRows})})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsx)(R.aR,{className:"pb-2",children:(0,a.jsx)(R.ZB,{className:"text-sm font-medium text-gray-600",children:"Hợp lệ"})}),(0,a.jsx)(R.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.summary.validRows})})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsx)(R.aR,{className:"pb-2",children:(0,a.jsx)(R.ZB,{className:"text-sm font-medium text-gray-600",children:"Cảnh b\xe1o"})}),(0,a.jsx)(R.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:t.summary.warningRows})})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsx)(R.aR,{className:"pb-2",children:(0,a.jsx)(R.ZB,{className:"text-sm font-medium text-gray-600",children:"Lỗi"})}),(0,a.jsx)(R.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.summary.errorRows})})]})]}),t.warnings.length>0&&(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-yellow-600 mt-0.5 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-yellow-800 mb-2",children:"Cảnh b\xe1o:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1 text-yellow-700",children:t.warnings.map((e,t)=>(0,a.jsx)("li",{className:"text-sm",children:e},t))})]})]})}),t.errors.length>0&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-red-600 mt-0.5 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-red-800 mb-2",children:"Lỗi:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1 text-red-700",children:t.errors.map((e,t)=>(0,a.jsx)("li",{className:"text-sm",children:e},t))})]})]})}),(0,a.jsx)(R.Zp,{children:(0,a.jsx)(R.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{children:t.summary.canImport?(0,a.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"C\xf3 thể import được"})]}):(0,a.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:"Kh\xf4ng thể import do c\xf3 lỗi"})]})}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsxs)(E,{variant:"outline",onClick:()=>o(!i),children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),i?"Ẩn chi tiết":"Xem chi tiết"]}),(0,a.jsxs)(E,{onClick:r,disabled:!t.summary.canImport||l,className:"bg-blue-600 hover:bg-blue-700",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),l?"Đang import...":"X\xe1c nhận import"]})]})]})})}),i&&(0,a.jsxs)(R.Zp,{children:[(0,a.jsx)(R.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(R.ZB,{children:"Chi tiết dữ liệu"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(E,{variant:"all"===d?"default":"outline",size:"sm",onClick:()=>u("all"),children:["Tất cả (",t.summary.totalRows,")"]}),(0,a.jsxs)(E,{variant:"valid"===d?"default":"outline",size:"sm",onClick:()=>u("valid"),children:["Hợp lệ (",t.summary.validRows,")"]}),(0,a.jsxs)(E,{variant:"warning"===d?"default":"outline",size:"sm",onClick:()=>u("warning"),children:["Cảnh b\xe1o (",t.summary.warningRows,")"]}),(0,a.jsxs)(E,{variant:"error"===d?"default":"outline",size:"sm",onClick:()=>u("error"),children:["Lỗi (",t.summary.errorRows,")"]})]})]})}),(0,a.jsx)(R.Wu,{children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full border-collapse border border-gray-300",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50",children:[(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"D\xf2ng"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Trạng th\xe1i"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Số thụ l\xfd"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Loại \xe1n"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Ng\xe0y thụ l\xfd"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Bị c\xe1o/NĐ/NKK"}),(0,a.jsx)("th",{className:"border border-gray-300 px-3 py-2 text-left",children:"Lỗi/Cảnh b\xe1o"})]})}),(0,a.jsx)("tbody",{children:m.map((e,t)=>(0,a.jsxs)("tr",{className:"\n                      ".concat("error"===e.status?"bg-red-50":"warning"===e.status?"bg-yellow-50":"bg-green-50","\n                    "),children:[(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.rowNumber}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(e=>{switch(e){case"valid":return(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,a.jsx)(y.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,a.jsx)(p.A,{className:"h-4 w-4 text-red-500"});default:return null}})(e.status),(0,a.jsx)("span",{className:"ml-2",children:(e=>{switch(e){case"valid":return(0,a.jsx)(B.Ex,{variant:"success",children:"Hợp lệ"});case"warning":return(0,a.jsx)(B.Ex,{variant:"warning",children:"Cảnh b\xe1o"});case"error":return(0,a.jsx)(B.Ex,{variant:"danger",children:"Lỗi"});default:return null}})(e.status)})]})}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.soThuLy}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.loaiAn}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.ngayThuLyDisplay||""}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2 max-w-xs truncate",children:e.biCaoNguoiKhieuKien}),(0,a.jsx)("td",{className:"border border-gray-300 px-3 py-2",children:e.validationErrors.length>0&&(0,a.jsx)("ul",{className:"text-sm text-red-600 space-y-1",children:e.validationErrors.map((e,t)=>(0,a.jsxs)("li",{children:["• ",e]},t))})})]},t))})]})})})]})]})},z=e=>{let{onImportComplete:t,onClose:r}=e,[l,c]=(0,s.useState)(null),[o,d]=(0,s.useState)(!1),[u,m]=(0,s.useState)(!1),[g,h]=(0,s.useState)(null),[x,p]=(0,s.useState)(null),y=(0,s.useRef)(null),f=async()=>{if(!l)return void n.oR.error("Vui l\xf2ng chọn file Excel");try{m(!0);let e=await i.previewImport(l);e.payload.success?h(e.payload.preview):n.oR.error("Kh\xf4ng thể xem trước file")}catch(e){console.error("Error previewing:",e),n.oR.error("C\xf3 lỗi xảy ra khi xem trước file")}finally{m(!1)}},b=async()=>{if(!l)return void n.oR.error("Vui l\xf2ng chọn file Excel");try{d(!0);let e=await i.importCourtCases(l);e.payload.success?(p(e.payload.results),h(null),n.oR.success("Import th\xe0nh c\xf4ng! ".concat(e.payload.results.success,"/").concat(e.payload.results.total," vụ việc")),t()):n.oR.error("Import thất bại")}catch(e){console.error("Error importing:",e),n.oR.error("C\xf3 lỗi xảy ra khi import file")}finally{d(!1)}},v=async()=>{try{await i.downloadTemplate(),n.oR.success("Đ\xe3 tải xuống file mẫu")}catch(e){console.error("Error downloading template:",e),n.oR.error("Kh\xf4ng thể tải xuống file mẫu")}};return(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,a.jsx)("div",{className:"p-6 rounded-lg max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:g?(0,a.jsx)(L,{preview:g,onConfirmImport:b,onCancel:()=>h(null),isImporting:o}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",style:{color:"#111827"},children:"\uD83D\uDCE5 Import vụ việc từ Excel"}),(0,a.jsx)("button",{onClick:r,className:"text-xl font-bold p-2 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",style:{color:"#111827"},children:"\uD83D\uDCCB Hướng dẫn:"}),(0,a.jsxs)("ul",{className:"text-sm space-y-1",style:{color:"#111827"},children:[(0,a.jsx)("li",{children:"• File Excel phải c\xf3 định dạng .xlsx hoặc .xls"}),(0,a.jsx)("li",{children:"• D\xf2ng đầu ti\xean l\xe0 ti\xeau đề cột (sẽ bị bỏ qua)"}),(0,a.jsx)("li",{children:"• STT c\xf3 thể để trống (hệ thống tự tạo)"}),(0,a.jsx)("li",{children:"• Ng\xe0y th\xe1ng theo định dạng YYYY-MM-DD hoặc DD/MM/YYYY"}),(0,a.jsx)("li",{children:"• C\xe1c trường kh\xe1c c\xf3 thể để trống"}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Khuyến kh\xedch xem trước dữ liệu trước khi import"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center p-4 border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCC4 File mẫu"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Tải xuống file mẫu để tham khảo định dạng"})]}),(0,a.jsx)("button",{onClick:v,className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Tải file mẫu"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",style:{color:"#111827"},children:"Chọn file Excel"}),(0,a.jsx)("input",{ref:y,type:"file",accept:".xlsx,.xls",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){if(!r.name.endsWith(".xlsx")&&!r.name.endsWith(".xls"))return void n.oR.error("Vui l\xf2ng chọn file Excel (.xlsx hoặc .xls)");c(r),p(null),h(null)}},className:"w-full p-2 border border-gray-300 rounded-md",style:{color:"#111827",backgroundColor:"#ffffff"}})]}),l&&(0,a.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("p",{className:"text-sm",style:{color:"#111827"},children:[(0,a.jsx)("strong",{children:"File đ\xe3 chọn:"})," ",l.name]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["K\xedch thước: ",(l.size/1024).toFixed(2)," KB"]})]})]}),x&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium",style:{color:"#111827"},children:"\uD83D\uDCCA Kết quả import:"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:x.total}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Tổng số d\xf2ng"})]}),(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:x.success}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Th\xe0nh c\xf4ng"})]}),(0,a.jsxs)("div",{className:"p-3 bg-yellow-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:x.duplicates}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Tr\xf9ng lặp"})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:x.errors.length}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Lỗi"})]})]}),x.errors.length>0&&(0,a.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-red-800 mb-2",children:"❌ Lỗi chi tiết:"}),(0,a.jsx)("div",{className:"max-h-32 overflow-y-auto",children:x.errors.map((e,t)=>(0,a.jsxs)("p",{className:"text-sm text-red-700",children:["• ",e]},t))})]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-4 pt-4 border-t",children:[(0,a.jsx)("button",{onClick:r,className:"px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50",style:{color:"#111827"},disabled:o||u,children:"Đ\xf3ng"}),(0,a.jsx)("button",{onClick:f,disabled:!l||o||u,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#059669",color:"#ffffff"},children:u?"Đang xem trước...":"\uD83D\uDC41️ Xem trước"}),(0,a.jsx)("button",{onClick:b,disabled:!l||o||u,className:"px-6 py-2 rounded-md hover:opacity-90 transition-opacity disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:o?"Đang import...":"\uD83D\uDCE5 Import trực tiếp"})]})]})]})})})},M=e=>{let{searchParams:t,onSearch:r,onReset:n}=e;s.useEffect(()=>{let e=document.createElement("style");return e.textContent="\n    .date-input::-webkit-calendar-picker-indicator {\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='currentColor'%3e%3cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd'/%3e%3c/svg%3e\");\n      cursor: pointer;\n    }\n    .date-input::-webkit-datetime-edit-text {\n      color: transparent;\n    }\n    .date-input::-webkit-datetime-edit-month-field {\n      color: transparent;\n    }\n    .date-input::-webkit-datetime-edit-day-field {\n      color: transparent;\n    }\n    .date-input::-webkit-datetime-edit-year-field {\n      color: transparent;\n    }\n    .date-input:focus::-webkit-datetime-edit-text,\n    .date-input:focus::-webkit-datetime-edit-month-field,\n    .date-input:focus::-webkit-datetime-edit-day-field,\n    .date-input:focus::-webkit-datetime-edit-year-field {\n      color: inherit;\n    }\n    .date-input[value]::-webkit-datetime-edit-text,\n    .date-input[value]::-webkit-datetime-edit-month-field,\n    .date-input[value]::-webkit-datetime-edit-day-field,\n    .date-input[value]::-webkit-datetime-edit-year-field {\n      color: inherit;\n    }\n  ",document.head.appendChild(e),()=>document.head.removeChild(e)},[]);let[l,i]=(0,s.useState)(!1),[c,o]=(0,s.useState)([]);(0,s.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await g.A.getCustomFields("CourtCase",e);t.payload.success&&o(t.payload.fields)}catch(e){console.error("Error fetching custom fields:",e)}})()},[]);let d=[{key:"soHoSo",label:"Số Hồ Sơ",type:"text",placeholder:"Nhập số hồ sơ (VD: 01/28/08/2025)"}],u=[...d];c.forEach(e=>{if("date"===e.dataType)u.push({key:e.name,label:e.label,type:"daterange",placeholder:"Chọn khoảng thời gian"});else{var t;u.push({key:e.name,label:e.label,type:"text"===e.dataType?"text":"number"===e.dataType?"number":"select"===e.dataType?"select":"text",options:"select"===e.dataType&&(null==(t=e.config)?void 0:t.options)?["",...e.config.options]:void 0,placeholder:"Nhập ".concat(e.label.toLowerCase())})}});let m=u.filter(e=>{if("daterange"===e.type){let r=t["".concat(e.key,"_from")],a=t["".concat(e.key,"_to")];return r&&""!==r||a&&""!==a}{let r=t[e.key];return r&&""!==r&&void 0!==r}}).length,h=(e,t)=>{r({[e]:t||void 0})},x=e=>{if(!e)return"";if(e.match(/^\d{4}-\d{2}-\d{2}$/))return e;if(e.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[t,r,a]=e.split("/");return"".concat(a,"-").concat(r,"-").concat(t)}return e},p=(e,t,r)=>{let a="".concat(e,"_to"),s=(e=>{if(!e)return"";if(e.match(/^\d{4}-\d{2}-\d{2}$/)){let[t,r,a]=e.split("-");return"".concat(a,"/").concat(r,"/").concat(t)}return e})(r);"from"===t?(h("".concat(e,"_from"),s),setTimeout(()=>{let e=document.querySelector('input[data-field="'.concat(a,'"]'));e&&r&&e.focus()},100)):h(a,s)};return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>i(!l),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"text-xl",children:"\uD83D\uDD0D"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Bộ lọc theo trường t\xf9y chỉnh"}),m>0&&(0,a.jsxs)("p",{className:"text-sm text-blue-600",children:[m," bộ lọc đang được \xe1p dụng"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[!l&&m>0&&(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),n()},className:"px-3 py-1 text-xs bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors",children:"X\xf3a tất cả"}),(0,a.jsx)("div",{className:"transform transition-transform duration-200 ".concat(l?"rotate-180":""),children:(0,a.jsx)("svg",{className:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,a.jsx)("div",{className:"transition-all duration-300 ease-in-out ".concat(l?"max-h-none opacity-100":"max-h-0 opacity-0 overflow-hidden"),children:(0,a.jsxs)("div",{className:"p-4 border-t border-gray-100",children:[d.length>0&&(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-4 flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83C\uDFDB️"}),"Bộ lọc hệ thống"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map(e=>(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-600 mb-2",children:e.label}),(e=>{let r=t[e.key]||"";return(0,a.jsx)("input",{type:"text",value:r,onChange:t=>h(e.key,t.target.value),placeholder:e.placeholder,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"})})(e)]},e.key))})]}),c.length>0?(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-4 flex items-center gap-2",children:[(0,a.jsx)("span",{children:"⚙️"}),"Bộ lọc theo trường t\xf9y chỉnh"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:u.slice(d.length).map(e=>(0,a.jsxs)("div",{className:"daterange"===e.type?"md:col-span-2":"",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2 flex items-center gap-1",children:["daterange"===e.type&&(0,a.jsx)("span",{children:"\uD83D\uDCC5"}),"select"===e.type&&(0,a.jsx)("span",{children:"\uD83D\uDCCB"}),"number"===e.type&&(0,a.jsx)("span",{children:"\uD83D\uDD22"}),"text"===e.type&&(0,a.jsx)("span",{children:"\uD83D\uDCDD"}),e.label,"daterange"===e.type&&(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"(Khoảng thời gian)"})]}),(e=>{switch(e.type){case"daterange":let r=t["".concat(e.key,"_from")]||"",s=t["".concat(e.key,"_to")]||"";return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("input",{type:"date",value:x(r),onChange:t=>p(e.key,"from",t.target.value),onFocus:e=>{var t;let r=null==(t=e.target.parentElement)?void 0:t.querySelector(".custom-placeholder");r&&(r.style.display="none")},onBlur:e=>{var t;let r=null==(t=e.target.parentElement)?void 0:t.querySelector(".custom-placeholder");r&&!e.target.value&&(r.style.display="block")},"data-field":"".concat(e.key,"_from"),className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent date-input",style:{colorScheme:"light"}}),!r&&(0,a.jsx)("div",{className:"custom-placeholder absolute left-2 top-2 text-gray-400 text-sm pointer-events-none",children:"dd/mm/yyyy"})]}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"→"}),(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("input",{type:"date",value:x(s),onChange:t=>p(e.key,"to",t.target.value),onFocus:e=>{var t;let r=null==(t=e.target.parentElement)?void 0:t.querySelector(".custom-placeholder");r&&(r.style.display="none")},onBlur:e=>{var t;let r=null==(t=e.target.parentElement)?void 0:t.querySelector(".custom-placeholder");r&&!e.target.value&&(r.style.display="block")},"data-field":"".concat(e.key,"_to"),className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent date-input",style:{colorScheme:"light"}}),!s&&(0,a.jsx)("div",{className:"custom-placeholder absolute left-2 top-2 text-gray-400 text-sm pointer-events-none",children:"dd/mm/yyyy"})]})]});case"select":var n;let l=t[e.key]||"";return(0,a.jsx)("select",{value:l,onChange:t=>h(e.key,t.target.value),className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",children:null==(n=e.options)?void 0:n.map((e,t)=>(0,a.jsx)("option",{value:e,children:e||"Tất cả"},t))});case"number":let i=t[e.key]||"";return(0,a.jsx)("input",{type:"number",value:i,onChange:t=>h(e.key,t.target.value),placeholder:e.placeholder,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"});default:let c=t[e.key]||"";return(0,a.jsx)("input",{type:"text",value:c,onChange:t=>h(e.key,t.target.value),placeholder:e.placeholder,className:"w-full p-2 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"})}})(e)]},e.key))})]}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDD"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Chưa c\xf3 trường t\xf9y chỉnh"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"Tạo c\xe1c trường t\xf9y chỉnh để c\xf3 thể lọc dữ liệu theo nhu cầu"}),(0,a.jsxs)("a",{href:"/dashboard/court-cases/custom-fields",className:"inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,a.jsx)("span",{children:"➕"}),"Tạo trường t\xf9y chỉnh"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m>0?(0,a.jsxs)("span",{className:"text-blue-600 font-medium",children:[m," bộ lọc đang hoạt động"]}):"Chưa c\xf3 bộ lọc n\xe0o được \xe1p dụng"}),(0,a.jsxs)("button",{onClick:n,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83D\uDDD1️"}),"X\xf3a tất cả bộ lọc"]})]})]})})]})};var I=r(48203);let q=()=>{let{hasPermission:e}=(0,m.S)(),[t,r]=(0,s.useState)([]),[l,c]=(0,s.useState)(!1),[o,d]=(0,s.useState)(null),[u,g]=(0,s.useState)(null),[h,x]=(0,s.useState)(!1),[p,y]=(0,s.useState)(!1),[f,b]=(0,s.useState)({page:1,limit:20,search:"",loaiAn:void 0,trangThaiGiaiQuyet:void 0,thuTucApDung:void 0,fromDate:"",toDate:"",sortBy:"createdAt",sortOrder:"desc"}),[v,j]=(0,s.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:20,hasNextPage:!1,hasPrevPage:!1}),w=async()=>{try{c(!0);let e=await i.getCourtCases(f);e.payload.success?(r(e.payload.cases),j(e.payload.pagination)):n.oR.error("Kh\xf4ng thể tải danh s\xe1ch vụ việc")}catch(e){console.error("Error fetching court cases:",e),n.oR.error("C\xf3 lỗi xảy ra khi tải danh s\xe1ch vụ việc")}finally{c(!1)}};(0,s.useEffect)(()=>{w()},[f]);let D=e=>{b(t=>({...t,page:e}))},S=async e=>{try{c(!0),(await i.createCourtCase(e)).payload.success?(n.oR.success("Th\xeam vụ việc th\xe0nh c\xf4ng"),x(!1),w()):n.oR.error("Kh\xf4ng thể th\xeam vụ việc")}catch(e){var t;console.error("Error creating court case:",e),n.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi th\xeam vụ việc")}finally{c(!1)}},T=async e=>{if(u)try{c(!0),(await i.updateCourtCase(u._id,e)).payload.success?(n.oR.success("Cập nhật vụ việc th\xe0nh c\xf4ng"),g(null),w()):n.oR.error("Kh\xf4ng thể cập nhật vụ việc")}catch(e){var t;console.error("Error updating court case:",e),n.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi cập nhật vụ việc")}finally{c(!1)}},A=async e=>{if(confirm("Bạn c\xf3 chắc chắn muốn x\xf3a vụ việc n\xe0y?"))try{(await i.deleteCourtCase(e)).payload.success?(n.oR.success("X\xf3a vụ việc th\xe0nh c\xf4ng"),w()):n.oR.error("Kh\xf4ng thể x\xf3a vụ việc")}catch(e){var t;console.error("Error deleting court case:",e),n.oR.error((null==(t=e.payload)?void 0:t.message)||"C\xf3 lỗi xảy ra khi x\xf3a vụ việc")}},E=async(e,t)=>{if("delete"===t)try{await i.bulkDeleteCourtCases(e),n.oR.success("Đ\xe3 x\xf3a ".concat(e.length," vụ việc")),w()}catch(e){console.error("Error bulk deleting:",e),n.oR.error("C\xf3 lỗi xảy ra khi x\xf3a h\xe0ng loạt")}},R=async()=>{try{n.oR.info("Đang xuất file Excel...");let e=await i.exportCourtCases(f),t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),r=window.URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download="danh-sach-vu-viec-".concat(new Date().toISOString().split("T")[0],".xlsx"),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(r),n.oR.success("Xuất file Excel th\xe0nh c\xf4ng")}catch(e){console.error("Error exporting Excel:",e),n.oR.error("C\xf3 lỗi xảy ra khi xuất file Excel")}};return(0,a.jsx)(I.default,{requiredPermissions:["court_case_view"],children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Quản l\xfd vụ việc t\xf2a \xe1n"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Quản l\xfd danh s\xe1ch thụ l\xfd v\xe0 giải quyết vụ việc đề nghị gi\xe1m đốc thẩm, t\xe1i thẩm"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[e("custom_fields_view")&&(0,a.jsx)("button",{onClick:()=>window.open("/dashboard/court-cases/custom-fields","_blank"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"⚙️ Quản l\xfd trường"}),e("court_case_import")&&(0,a.jsx)("button",{onClick:()=>y(!0),className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500",children:"\uD83D\uDCE5 Import Excel"}),e("court_case_export")&&(0,a.jsx)("button",{onClick:R,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500",children:"\uD83D\uDCCA Xuất Excel"}),e("court_case_create")&&(0,a.jsx)("button",{onClick:()=>x(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"➕ Th\xeam vụ việc mới"})]})]}),(0,a.jsx)(M,{searchParams:f,onSearch:e=>{b(t=>({...t,...e,page:1}))},onReset:()=>{b({page:1,limit:20,sortBy:"createdAt",sortOrder:"desc"})}}),(0,a.jsx)(N,{cases:t,onCaseSelect:d,onCaseEdit:g,onCaseDelete:A,onBulkAction:E,onSort:(e,t)=>{b(r=>({...r,sortBy:e,sortOrder:t,page:1}))},currentSort:{sortBy:f.sortBy,sortOrder:f.sortOrder},loading:l}),v.totalPages>1&&(0,a.jsx)("div",{className:"bg-white px-6 py-3 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-700",children:["Hiển thị ",(v.currentPage-1)*v.itemsPerPage+1," đến"," ",Math.min(v.currentPage*v.itemsPerPage,v.totalItems)," trong tổng số"," ",v.totalItems," vụ việc"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>D(v.currentPage-1),disabled:!v.hasPrevPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Trước"}),(0,a.jsxs)("span",{className:"px-3 py-2 text-sm text-gray-700",children:["Trang ",v.currentPage," / ",v.totalPages]}),(0,a.jsx)("button",{onClick:()=>D(v.currentPage+1),disabled:!v.hasNextPage,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Sau"})]})]})}),h&&(0,a.jsx)(C,{onSubmit:S,onCancel:()=>x(!1),loading:l}),u&&(0,a.jsx)(C,{courtCase:u,onSubmit:T,onCancel:()=>g(null),loading:l}),o&&(0,a.jsx)(k,{courtCase:o,onClose:()=>d(null),onEdit:g,onDelete:A}),p&&(0,a.jsx)(z,{onImportComplete:()=>{w()},onClose:()=>y(!1)})]})})}},26983:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},42529:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},46673:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,TL:()=>l});var a=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var n=r(95155);function l(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...n}=e;if(a.isValidElement(r)){var l;let e,i,c=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),o=function(e,t){let r={...t};for(let a in t){let s=e[a],n=t[a];/^on[A-Z]/.test(a)?s&&n?r[a]=(...e)=>{let t=n(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...n}:"className"===a&&(r[a]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==a.Fragment&&(o.ref=t?function(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}(t,c):c),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...l}=e,i=a.Children.toArray(s),c=i.find(o);if(c){let e=c.props.children,s=i.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...l,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),c=Symbol("radix.slottable");function o(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},47972:(e,t,r)=>{"use strict";r.d(t,{CN:()=>c,Lz:()=>o,Wu:()=>i,ZB:()=>l,Zp:()=>s,aR:()=>n});var a=r(95155);r(12115);let s=e=>{let{children:t,className:r="",padding:s="md",shadow:n="sm",hover:l=!1,onClick:i}=e;return(0,a.jsx)("div",{className:"\n        bg-white rounded-xl border border-gray-100\n        ".concat({none:"",sm:"p-4",md:"p-6",lg:"p-8"}[s],"\n        ").concat({none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[n],"\n        ").concat(l?"hover:shadow-lg transition-shadow duration-200":"","\n        ").concat(r,"\n      "),onClick:i,children:t})},n=e=>{let{children:t,className:r=""}=e;return(0,a.jsx)("div",{className:"border-b border-gray-100 pb-4 mb-6 ".concat(r),children:t})},l=e=>{let{children:t,className:r="",size:s="md"}=e;return(0,a.jsx)("h2",{className:"font-bold text-gray-900 ".concat({sm:"text-lg",md:"text-xl",lg:"text-2xl"}[s]," ").concat(r),children:t})},i=e=>{let{children:t,className:r=""}=e;return(0,a.jsx)("div",{className:r,children:t})},c=e=>{let{title:t,value:r,icon:n,trend:l,color:i="blue",onClick:c,clickable:o=!1}=e,d=(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:t}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof r?r.toLocaleString():r}),l&&(0,a.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat(l.isPositive?"text-green-600":"text-red-600"),children:[(0,a.jsx)("span",{className:"mr-1",children:l.isPositive?"↗":"↘"}),l.value]})]}),n&&(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[i]),children:(0,a.jsx)("div",{className:"text-white",children:n})})]});return o&&c?(0,a.jsx)(s,{hover:!0,className:"relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ".concat(o?"hover:shadow-lg":""),onClick:c,children:d}):(0,a.jsx)(s,{hover:!0,className:"relative overflow-hidden",children:d})},o=e=>{let{title:t,description:r,icon:n,onClick:l,href:i,color:c="blue"}=e,o=e=>{let{children:t}=e;return(0,a.jsx)(s,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:t})},d=(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[n&&(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[c]," flex-shrink-0"),children:(0,a.jsx)("div",{className:"text-white",children:n})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:r})]})]});return i?(0,a.jsx)("a",{href:i,children:(0,a.jsx)(o,{children:d})}):(0,a.jsx)("div",{onClick:l,children:(0,a.jsx)(o,{children:d})})}},52056:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},52726:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(25656);let s={getDateCountdowns:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return a.Ay.get("/api/date-countdowns?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},upsertDateCountdown:(e,t)=>a.Ay.post("/api/date-countdowns",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteDateCountdown:(e,t)=>a.Ay.delete("/api/date-countdowns/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),getCountdownStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return a.Ay.get("/api/date-countdowns/stats?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},getUpcomingDeadlines:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,r=arguments.length>2?arguments[2]:void 0;return a.Ay.get("/api/date-countdowns/upcoming?targetModel=".concat(e,"&days=").concat(t),{headers:{Authorization:"Bearer ".concat(r)}})}}},52799:(e,t,r)=>{"use strict";r.d(t,{Ex:()=>s,eG:()=>n});var a=r(95155);r(12115);let s=e=>{let{children:t,variant:r="default",size:s="md",className:n="",dot:l=!1}=e;return(0,a.jsxs)("span",{className:"\n        ".concat("inline-flex items-center font-medium rounded-full","\n        ").concat({default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",warning:"bg-yellow-100 text-yellow-800",danger:"bg-red-100 text-red-800",info:"bg-blue-100 text-blue-800",secondary:"bg-purple-100 text-purple-800"}[r],"\n        ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[s],"\n        ").concat(n,"\n      "),children:[l&&(0,a.jsx)("span",{className:"w-2 h-2 rounded-full mr-2 ".concat({default:"bg-gray-500",success:"bg-green-500",warning:"bg-yellow-500",danger:"bg-red-500",info:"bg-blue-500",secondary:"bg-purple-500"}[r])}),t]})},n=e=>{let{role:t,className:r=""}=e,n={admin:{label:"Quản trị vi\xean",variant:"danger"},department_manager:{label:"Quản l\xfd ph\xf2ng ban",variant:"warning"},department_member:{label:"Th\xe0nh vi\xean ph\xf2ng ban",variant:"info"},member:{label:"Th\xe0nh vi\xean",variant:"info"},manager:{label:"Quản l\xfd",variant:"info"},editor:{label:"Bi\xean tập vi\xean",variant:"secondary"},user:{label:"Người d\xf9ng",variant:"default"}},l=n[t]||n.user;return(0,a.jsx)(s,{variant:l.variant,className:r,children:l.label})}},57052:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(25656);let s={getCustomFields:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return a.Ay.get("/api/custom-fields?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},createCustomField:(e,t)=>a.Ay.post("/api/custom-fields",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateCustomField:(e,t,r)=>a.Ay.put("/api/custom-fields/".concat(e),t,{headers:{Authorization:"Bearer ".concat(r)}}),deleteCustomField:(e,t)=>a.Ay.delete("/api/custom-fields/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldsOrder:(e,t)=>a.Ay.put("/api/custom-fields/order",{fieldOrders:e},{headers:{Authorization:"Bearer ".concat(t)}}),getCustomFieldStats:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return a.Ay.get("/api/custom-fields/stats?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},getFieldsInDatabase:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase";return a.Ay.get("/api/custom-fields/database-fields?targetModel=".concat(e))}}},57828:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},65229:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71360:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},78874:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(71847).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},83101:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var a=r(2821);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,l=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,c=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let n=s(t)||s(a);return l[e][n]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,c,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...o}[t]):({...i,...o})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},84961:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(25656);let s={getFieldConfiguration:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"CourtCase",t=arguments.length>1?arguments[1]:void 0;return a.Ay.get("/api/field-configurations?targetModel=".concat(e),{headers:{Authorization:"Bearer ".concat(t)}})},updateFieldConfiguration:(e,t)=>a.Ay.put("/api/field-configurations",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldOrder:(e,t)=>a.Ay.put("/api/field-configurations/order",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateFieldVisibility:(e,t)=>a.Ay.put("/api/field-configurations/visibility",e,{headers:{Authorization:"Bearer ".concat(t)}})}},92767:(e,t,r)=>{Promise.resolve().then(r.bind(r,20929))}},e=>{e.O(0,[9268,2739,4744,4595,8441,1255,7358],()=>e(e.s=92767)),_N_E=e.O()}]);