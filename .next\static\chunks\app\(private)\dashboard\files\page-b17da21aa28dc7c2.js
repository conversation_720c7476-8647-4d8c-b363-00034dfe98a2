(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6156],{13331:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(95155),r=s(12115),l=s(71847);let i=(0,l.A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);var n=s(21786),c=s(74744),o=s(25656);let d={getFiles:(e,t)=>o.Ay.post("/api/administrator/files",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteFile:(e,t)=>o.Ay.delete("/api/administrator/files/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),bulkAction:(e,t)=>o.Ay.post("/api/administrator/files/bulk-action",e,{headers:{Authorization:"Bearer ".concat(t)}}),getFileStats:e=>o.Ay.get("/api/administrator/files/stats",{headers:{Authorization:"Bearer ".concat(e)}}),getSyncStatus:e=>o.Ay.get("/api/administrator/files/sync-status",{headers:{Authorization:"Bearer ".concat(e)}}),syncExistingFiles:e=>o.Ay.post("/api/administrator/files/sync-existing",{},{headers:{Authorization:"Bearer ".concat(e)}})},x=(0,l.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),u=(0,l.A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]);var h=s(89715),m=s(80534);let g=(0,l.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);var p=s(97849);let y=e=>{let{files:t,onFileSelect:s,onFileDelete:l,onBulkAction:n,loading:o=!1}=e,[d,y]=(0,r.useState)([]),[f,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{b(d.length===t.length&&t.length>0)},[d,t]);let v=e=>{if(0===d.length)return void c.oR.warning("Please select files first");confirm("delete"===e?"Are you sure you want to delete ".concat(d.length," files?"):"Are you sure you want to ".concat(e," ").concat(d.length," files?"))&&(n(d,e),y([]),b(!1))};return o?(0,a.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading files..."})]}):0===t.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"flex justify-center mb-2",children:(0,a.jsx)(i,{size:48,className:"text-gray-400"})}),(0,a.jsx)("p",{children:"No files found"})]}):(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[d.length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 border-b px-4 py-3 flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-blue-700",children:[d.length," files selected"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>v("activate"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Activate"}),(0,a.jsx)("button",{onClick:()=>v("deactivate"),className:"px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700",children:"Deactivate"}),(0,a.jsx)("button",{onClick:()=>v("delete"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"Delete"})]})]}),(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left",children:(0,a.jsx)("input",{type:"checkbox",checked:f,onChange:()=>{f?y([]):y(t.map(e=>e._id)),b(!f)},className:"rounded border-gray-300"})}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded By"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 ".concat(d.includes(e._id)?"bg-blue-50":""),children:[(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)("input",{type:"checkbox",checked:d.includes(e._id),onChange:()=>(e=>{y(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])})(e._id),className:"rounded border-gray-300"})}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:((e,t)=>{switch(e){case"image":return(0,a.jsx)(x,{size:20,className:"text-blue-500"});case"video":return(0,a.jsx)(u,{size:20,className:"text-purple-500"});case"document":if(t.includes("pdf"))return(0,a.jsx)(h.A,{size:20,className:"text-red-500"});if(t.includes("word"))return(0,a.jsx)(h.A,{size:20,className:"text-blue-600"});if(t.includes("excel"))return(0,a.jsx)(m.A,{size:20,className:"text-green-600"});return(0,a.jsx)(h.A,{size:20,className:"text-gray-500"});default:return(0,a.jsx)(g,{size:20,className:"text-gray-500"})}})(e.type,e.mimetype)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate max-w-xs",children:e.originalName}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate max-w-xs",children:e.filename})]})]})}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:e.type})}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,p.z3)(e.size)}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.uploadedBy.username}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,p.Yq)(e.uploadedAt)}),(0,a.jsx)("td",{className:"px-4 py-3",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>s(e),className:"text-blue-600 hover:text-blue-900",children:"View"}),(0,a.jsx)("button",{onClick:()=>window.open(e.url,"_blank"),className:"text-green-600 hover:text-green-900",children:"Download"}),(0,a.jsx)("button",{onClick:()=>l(e._id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e._id))})]})})]})},f=e=>{let{onSyncComplete:t}=e,[s,l]=(0,r.useState)(null),[i,n]=(0,r.useState)(!1),[o,x]=(0,r.useState)(!1),u=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await d.getSyncStatus(e);t.payload.success&&l(t.payload.status)}catch(e){console.error("Error fetching sync status:",e)}},h=async()=>{if(!s||0===s.missingInDatabase)return void c.oR.info("Kh\xf4ng c\xf3 file n\xe0o cần đồng bộ");if(confirm("Bạn c\xf3 chắc muốn đồng bộ ".concat(s.missingInDatabase," files? Qu\xe1 tr\xecnh n\xe0y c\xf3 thể mất một \xedt thời gian.")))try{n(!0);let e=localStorage.getItem("sessionToken")||"";(await d.syncExistingFiles(e)).payload.success?(c.oR.success("Đ\xe3 bắt đầu đồng bộ file. Vui l\xf2ng kiểm tra lại sau v\xe0i ph\xfat."),setTimeout(()=>{u(),null==t||t()},3e3)):c.oR.error("Kh\xf4ng thể bắt đầu đồng bộ file")}catch(e){console.error("Error syncing files:",e),c.oR.error("Đ\xe3 xảy ra lỗi khi đồng bộ file")}finally{n(!1)}};if((0,r.useEffect)(()=>{u();let e=setInterval(u,3e4);return()=>clearInterval(e)},[]),!s)return(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,a.jsx)("div",{className:"h-6 bg-gray-300 rounded w-1/2"})]})});let m=0===s.missingInDatabase;return(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:i?"\uD83D\uDD04":m?"✅":"⚠️"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"File Sync Status"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Trạng th\xe1i đồng bộ giữa thư mục vật l\xfd v\xe0 database"})]})]}),(0,a.jsx)("button",{onClick:()=>x(!o),className:"text-blue-600 hover:text-blue-800 text-sm",children:o?"Ẩn chi tiết":"Xem chi tiết"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:s.totalPhysicalFiles}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Files vật l\xfd"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:s.totalDatabaseFiles}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Files trong DB"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,a.jsx)("div",{className:"text-2xl font-bold ".concat(s.missingInDatabase>0?"text-orange-600":"text-green-600"),children:s.missingInDatabase}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Chưa đồng bộ"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,a.jsx)("div",{className:"text-2xl font-bold ".concat(m?"text-green-600":"text-orange-600"),children:m?"100%":Math.round(s.totalDatabaseFiles/s.totalPhysicalFiles*100)+"%"}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Tỉ lệ sync"})]})]}),(0,a.jsx)("div",{className:"p-3 rounded-md mb-4 ".concat(m?"bg-green-50 text-green-800":"bg-orange-50 text-orange-800"),children:i?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}),"Đang đồng bộ files..."]}):m?"✅ Tất cả files đ\xe3 được đồng bộ":"⚠️ C\xf3 ".concat(s.missingInDatabase," files chưa được đồng bộ v\xe0o database")}),!m&&!i&&(0,a.jsxs)("button",{onClick:h,disabled:i,className:"w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50",children:["\uD83D\uDD04 Đồng bộ ",s.missingInDatabase," files"]}),o&&(0,a.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Files chưa đồng bộ (10 đầu ti\xean):"}),s.missingFiles&&s.missingFiles.length>0?(0,a.jsxs)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[s.missingFiles.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded text-sm",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"truncate font-medium text-gray-900",children:e.filename}),(0,a.jsx)("div",{className:"text-gray-500 text-xs",children:e.relativePath})]}),(0,a.jsx)("div",{className:"ml-4 text-gray-600 text-xs",children:(0,p.z3)(e.size)})]},t)),s.missingInDatabase>s.missingFiles.length&&(0,a.jsxs)("div",{className:"text-center text-gray-500 text-sm py-2",children:["... v\xe0 ",s.missingInDatabase-s.missingFiles.length," files kh\xe1c"]})]}):(0,a.jsx)("div",{className:"text-gray-500 text-sm",children:"Kh\xf4ng c\xf3 files n\xe0o cần đồng bộ"})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-between items-center text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Cập nhật lần cuối: ",new Date().toLocaleTimeString()]}),(0,a.jsx)("button",{onClick:u,className:"text-blue-600 hover:text-blue-800",children:"\uD83D\uDD04 L\xe0m mới"})]})]})};var b=s(48203);let v=()=>{let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[o,x]=(0,r.useState)(null),[u,h]=(0,r.useState)({page:1,perPage:20,type:"all",sortBy:"uploadedAt",sortOrder:"desc"}),[m,g]=(0,r.useState)(0),[v,j]=(0,r.useState)(null),[N,w]=(0,r.useState)(!1),k=async()=>{try{l(!0);let e=localStorage.getItem("sessionToken")||"",s=await d.getFiles(u,e);s.payload.success?(t(s.payload.files),g(s.payload.total||0)):c.oR.error("Failed to fetch files")}catch(e){console.error("Error fetching files:",e),c.oR.error("An error occurred while fetching files")}finally{l(!1)}},A=async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await d.getFileStats(e);t.payload.success&&x(t.payload.stats)}catch(e){console.error("Error fetching stats:",e)}},S=async e=>{if(confirm("Are you sure you want to delete this file?"))try{let t=localStorage.getItem("sessionToken")||"";(await d.deleteFile(e,t)).payload.success?(c.oR.success("File deleted successfully"),k(),A()):c.oR.error("Failed to delete file")}catch(e){console.error("Error deleting file:",e),c.oR.error("An error occurred while deleting file")}},C=async(e,t)=>{try{let s=localStorage.getItem("sessionToken")||"";(await d.bulkAction({fileIds:e,action:t},s)).payload.success?(c.oR.success("".concat(t," completed successfully")),k(),A()):c.oR.error("Failed to ".concat(t," files"))}catch(e){console.error("Error in bulk ".concat(t,":"),e),c.oR.error("An error occurred while performing ".concat(t))}},D=(e,t)=>{h(s=>({...s,[e]:t,page:1}))},F=e=>{h(t=>({...t,page:e}))};(0,r.useEffect)(()=>{k()},[u]),(0,r.useEffect)(()=>{A()},[]);let _=Math.ceil(m/u.perPage);return(0,a.jsx)(b.default,{requiredPermissions:["file_view","file_upload","file_delete"],requireAll:!1,children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"File Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage uploaded files and media"})]}),o&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"mr-3",children:(0,a.jsx)(i,{size:32,className:"text-blue-500"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Files"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:(0,p.ZV)(o.totalFiles)})]})]})}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Total Size"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:(0,p.z3)(o.totalSize)})]})]})}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC8"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Recent Uploads"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:(0,p.ZV)(o.recentUploads)})]})]})}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-2xl mr-3",children:"\uD83C\uDFAF"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"File Types"}),(0,a.jsx)("p",{className:"text-xl font-semibold",children:o.filesByType.length})]})]})})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(f,{onSyncComplete:()=>{k(),A()}})}),(0,a.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,a.jsx)("div",{className:"flex-1 min-w-64",children:(0,a.jsx)("input",{type:"text",placeholder:"Search files...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>{var t;return t=e.target.value,void h(e=>({...e,query:t,page:1}))}})}),(0,a.jsxs)("select",{value:u.type,onChange:e=>D("type",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"image",children:"Images"}),(0,a.jsx)("option",{value:"video",children:"Videos"}),(0,a.jsx)("option",{value:"document",children:"Documents"}),(0,a.jsx)("option",{value:"other",children:"Other"})]}),(0,a.jsxs)("select",{value:"".concat(u.sortBy,"-").concat(u.sortOrder),onChange:e=>{let[t,s]=e.target.value.split("-");D("sortBy",t),D("sortOrder",s)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"uploadedAt-desc",children:"Newest First"}),(0,a.jsx)("option",{value:"uploadedAt-asc",children:"Oldest First"}),(0,a.jsx)("option",{value:"filename-asc",children:"Name A-Z"}),(0,a.jsx)("option",{value:"filename-desc",children:"Name Z-A"}),(0,a.jsx)("option",{value:"size-desc",children:"Largest First"}),(0,a.jsx)("option",{value:"size-asc",children:"Smallest First"})]}),(0,a.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)(n.A,{size:16}),"Upload File"]})]})}),(0,a.jsx)(y,{files:e,onFileSelect:j,onFileDelete:S,onBulkAction:C,loading:s}),_>1&&(0,a.jsx)("div",{className:"mt-6 flex justify-center",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>F(u.page-1),disabled:u.page<=1,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:Math.min(5,_)},(e,t)=>{let s=t+1;return(0,a.jsx)("button",{onClick:()=>F(s),className:"px-3 py-2 border rounded-md ".concat(u.page===s?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"),children:s},s)}),(0,a.jsx)("button",{onClick:()=>F(u.page+1),disabled:u.page>=_,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})}),v&&(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,a.jsxs)("div",{className:"p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",style:{color:"#111827"},children:"File Details"}),(0,a.jsx)("button",{onClick:()=>j(null),className:"text-xl font-bold p-1 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,a.jsxs)("div",{className:"space-y-3",style:{color:"#111827"},children:[(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Name:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.originalName})]}),(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Type:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.type})]}),(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Size:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:(0,p.z3)(v.size)})]}),(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Uploaded by:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.uploadedBy.username})]}),(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Upload date:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:new Date(v.uploadedAt).toLocaleString()})]}),(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Status:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.isActive?"Active":"Inactive"})]}),v.description&&(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Description:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.description})]}),v.tags&&v.tags.length>0&&(0,a.jsxs)("div",{style:{color:"#111827"},children:[(0,a.jsx)("strong",{style:{color:"#111827"},children:"Tags:"}),(0,a.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:v.tags.join(", ")})]})]}),(0,a.jsxs)("div",{className:"mt-6 flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>window.open(v.url,"_blank"),className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:"View/Download"}),(0,a.jsx)("button",{onClick:()=>{S(v._id),j(null)},className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"Delete"})]})]})})]})})}},21786:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},25656:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d});var a=s(79902),r=s(64269),l=s(20063);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class n extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let c=null,o=async(e,t,s)=>{let o;(null==s?void 0:s.body)instanceof FormData?o=s.body:(null==s?void 0:s.body)&&(o=JSON.stringify(s.body));let d=o instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let x=(null==s?void 0:s.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:s.baseUrl,u=t.startsWith("/")?"".concat(x).concat(t):"".concat(x,"/").concat(t),h=await fetch(u,{...s,headers:{...d,...null==s?void 0:s.headers},body:o,method:e}),m=null,g=h.headers.get("content-type");if(g&&g.includes("application/json"))try{m=await h.json()}catch(e){console.error("Failed to parse JSON response:",e),m=null}else m=await h.text();let p={status:h.status,payload:m};if(!h.ok)if(404===h.status||403===h.status)throw new n(p);else if(401===h.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,l.redirect)("/logout?sessionToken=".concat(e))}else if(!c){c=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await c}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),c=null,location.href="/login"}}}else throw new i(p);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,r.Fd)(t))){let{token:e}=m;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,r.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return p},d={get:(e,t)=>o("GET",e,t),post:(e,t,s)=>o("POST",e,{...s,body:t}),put:(e,t,s)=>o("PUT",e,{...s,body:t}),patch:(e,t,s)=>o("PATCH",e,{...s,body:t}),delete:(e,t)=>o("DELETE",e,{...t})}},25679:(e,t,s)=>{Promise.resolve().then(s.bind(s,13331))},48203:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var a=s(95155),r=s(75444),l=s(20063),i=s(12115);function n(e){let{children:t,requiredPermission:s,requiredPermissions:n=[],requireAll:c=!1,fallbackPath:o="/dashboard"}=e,{hasPermission:d,hasAnyPermission:x,isAdmin:u,isDepartmentManager:h,isLoading:m}=(0,r.S)(),g=(0,l.useRouter)();if((0,i.useEffect)(()=>{if(!m&&!u)(s?"admin"===s&&!!h||d(s):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))||g.replace(o)},[d,x,u,h,m,s,n,c,o,g]),m)return(0,a.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(u)return(0,a.jsx)(a.Fragment,{children:t});return(s?"admin"===s&&!!h||d(s):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,a.jsx)("button",{onClick:()=>g.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},64269:(e,t,s)=>{"use strict";s.d(t,{Fd:()=>i,cn:()=>l}),s(25656);var a=s(2821),r=s(75889);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}s(138);let i=e=>e.startsWith("/")?e.slice(1):e},71847:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var a=s(12115);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:x,...u}=e;return(0,a.createElement)("svg",{ref:t,...i,width:r,height:r,stroke:s,strokeWidth:c?24*Number(n)/Number(r):n,className:l("lucide",o),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...x.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let s=(0,a.forwardRef)((s,i)=>{let{className:c,...o}=s;return(0,a.createElement)(n,{ref:i,iconNode:t,className:l("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...o})});return s.displayName=r(e),s}},75444:(e,t,s)=>{"use strict";s.d(t,{S:()=>r});var a=s(97367);let r=()=>{let{user:e,isLoading:t}=(0,a.U)();return{hasPermission:s=>{var a;return!t&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(s))||!1)},hasAnyPermission:s=>!t&&!!e&&("admin"===e.rule||s.some(t=>{var s;return null==(s=e.permissions)?void 0:s.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isDepartmentManager:!t&&(null==e?void 0:e.rule)==="department_manager",isLoading:t}}},79902:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var a=s(77376),r=s(95704);let l=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:r.env.CRYPTOJS_SECRECT});if(!l.success)throw console.error("Invalid environment variables:",l.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=l.data},80534:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},89715:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(71847).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},97367:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,default:()=>n});var a=s(95155),r=s(12115);let l=(0,r.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,r.useContext)(l),n=e=>{let{children:t}=e,[s,i]=(0,r.useState)(()=>null),[n,c]=(0,r.useState)(!0),o=(0,r.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,r.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),c(!1)},[i]),(0,a.jsx)(l.Provider,{value:{user:s,setUser:o,isAuthenticated:!!s,isLoading:n},children:t})}},97849:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>r,ZV:()=>l,z3:()=>a});let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(t<0?0:t))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][s]},r=e=>{let t;if(!e)return"";if("string"==typeof e){if(e.match(/^\d{2}\/\d{2}\/\d{4}$/))return e;if(e.match(/^\d{2}\/\d{2}\/\d{4}$/)){let[s,a,r]=e.split("/");t=new Date(parseInt(r),parseInt(a)-1,parseInt(s))}else t=new Date(e)}else t=new Date(e);if(isNaN(t.getTime()))return"";let s=t.getDate().toString().padStart(2,"0"),a=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getFullYear().toString();return"".concat(s,"/").concat(a,"/").concat(r)},l=e=>e.toLocaleString()}},e=>{e.O(0,[9268,2739,4744,8441,1255,7358],()=>e(e.s=25679)),_N_E=e.O()}]);