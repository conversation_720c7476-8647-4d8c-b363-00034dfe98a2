(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3664],{11925:(e,r,n)=>{"use strict";n.d(r,{A:()=>l});var t=n(12115),s=n(12758),o=n.n(s);function i(){return(i=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}var a=(0,t.forwardRef)(function(e,r){var n=e.color,s=e.size,o=void 0===s?24:s,a=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return t.createElement("svg",i({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),t.createElement("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"}))});a.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},a.displayName="Shield";let l=a},34502:(e,r,n)=>{"use strict";n.d(r,{he:()=>o,v_:()=>t});let t=[{id:"user_view",name:"Xem danh s\xe1ch người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể xem danh s\xe1ch v\xe0 th\xf4ng tin người d\xf9ng"},{id:"user_add",name:"Th\xeam người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể tạo t\xe0i khoản người d\xf9ng mới"},{id:"user_edit",name:"Chỉnh sửa người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể chỉnh sửa th\xf4ng tin người d\xf9ng"},{id:"user_delete",name:"X\xf3a người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể x\xf3a t\xe0i khoản người d\xf9ng"},{id:"user_import",name:"Import người d\xf9ng",category:"Quản l\xfd người d\xf9ng",description:"C\xf3 thể import danh s\xe1ch người d\xf9ng từ file"},{id:"file_view",name:"Xem file",category:"Quản l\xfd file",description:"C\xf3 thể xem danh s\xe1ch file"},{id:"file_upload",name:"Upload file",category:"Quản l\xfd file",description:"C\xf3 thể upload file l\xean hệ thống"},{id:"file_delete",name:"X\xf3a file",category:"Quản l\xfd file",description:"C\xf3 thể x\xf3a file khỏi hệ thống"},{id:"file_download",name:"Tải file",category:"Quản l\xfd file",description:"C\xf3 thể tải file về m\xe1y"},{id:"court_case_view",name:"Xem vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 chi tiết vụ \xe1n"},{id:"court_case_create",name:"Tạo vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể tạo vụ \xe1n mới"},{id:"court_case_edit",name:"Chỉnh sửa vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể chỉnh sửa th\xf4ng tin vụ \xe1n"},{id:"court_case_delete",name:"X\xf3a vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể x\xf3a vụ \xe1n"},{id:"court_case_export",name:"Xuất dữ liệu vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xuất dữ liệu vụ \xe1n ra file"},{id:"court_case_import",name:"Import vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể import dữ liệu vụ \xe1n từ file"},{id:"court_case_stats",name:"Thống k\xea vụ \xe1n",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem thống k\xea v\xe0 b\xe1o c\xe1o về vụ \xe1n"},{id:"custom_fields_view",name:"Xem trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể xem danh s\xe1ch v\xe0 cấu h\xecnh trường t\xf9y chỉnh"},{id:"custom_fields_create",name:"Tạo trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể tạo trường t\xf9y chỉnh mới"},{id:"custom_fields_edit",name:"Chỉnh sửa trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể chỉnh sửa cấu h\xecnh trường t\xf9y chỉnh"},{id:"custom_fields_delete",name:"X\xf3a trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể x\xf3a trường t\xf9y chỉnh"},{id:"custom_fields_manage",name:"Quản l\xfd to\xe0n bộ trường t\xf9y chỉnh",category:"Quản l\xfd vụ \xe1n",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống trường t\xf9y chỉnh"},{id:"department_view",name:"Xem ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể xem danh s\xe1ch ph\xf2ng ban"},{id:"department_create",name:"Tạo ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể tạo ph\xf2ng ban mới"},{id:"department_edit",name:"Chỉnh sửa ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể chỉnh sửa th\xf4ng tin ph\xf2ng ban"},{id:"department_delete",name:"X\xf3a ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể x\xf3a ph\xf2ng ban"},{id:"department_member_manage",name:"Quản l\xfd th\xe0nh vi\xean ph\xf2ng ban",category:"Quản l\xfd ph\xf2ng ban",description:"C\xf3 thể th\xeam, sửa, x\xf3a th\xe0nh vi\xean ph\xf2ng ban"},{id:"permissions_manage",name:"Quản l\xfd quyền",category:"Quản l\xfd quyền",description:"C\xf3 thể cấp v\xe0 thu hồi quyền của th\xe0nh vi\xean"},{id:"system_settings_view",name:"Xem c\xe0i đặt hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể xem c\xe0i đặt hệ thống"},{id:"system_settings_edit",name:"Chỉnh sửa c\xe0i đặt hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể chỉnh sửa c\xe0i đặt hệ thống"},{id:"system_logs_view",name:"Xem nhật k\xfd hệ thống",category:"C\xe0i đặt hệ thống",description:"C\xf3 thể xem nhật k\xfd hoạt động hệ thống"},{id:"system_admin_full_access",name:"Quyền admin to\xe0n quyền",category:"Quyền đặc biệt",description:"C\xf3 to\xe0n quyền truy cập hệ thống"},{id:"system_departments_manage",name:"Quản l\xfd hệ thống ph\xf2ng ban",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống ph\xf2ng ban"},{id:"system_users_manage",name:"Quản l\xfd hệ thống người d\xf9ng",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ hệ thống người d\xf9ng"},{id:"system_settings_manage",name:"Quản l\xfd c\xe0i đặt hệ thống",category:"Quyền đặc biệt",description:"C\xf3 thể quản l\xfd to\xe0n bộ c\xe0i đặt hệ thống"}],s=new Map;function o(e){let r=s.get(e);return r?r.name:e}t.forEach(e=>{s.set(e.id,e)})},38867:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>z});var t=n(95155),s=n(12115),o=n(43281),i=n(65142),a=n(56599),l=n(22544),c=n(41502),d=n(33602),h=n(20063);let u=e=>{let{user:r,onSubmit:n,onSubmitPass:u}=e,[m,g]=(0,s.useState)(!1);(0,h.useRouter)();let[p,x]=(0,s.useState)(null),y=["Male","Female","Not"],f=(0,l.mN)({resolver:(0,d.u)(c.aP),defaultValues:r||{_id:"",email:"",username:"",phonenumber:"",private:!1,gender:"Not",bio:"",permissions:[]}});s.useEffect(()=>{r&&(console.log("Resetting form with user data:",r),f.reset(r))},[r,f]),s.useEffect(()=>{console.log("Form state:",{isValid:f.formState.isValid,errors:f.formState.errors,values:f.getValues()}),Object.keys(f.formState.errors).length>0&&(console.log("Detailed validation errors:"),Object.entries(f.formState.errors).forEach(e=>{let[r,n]=e;console.log('Field "'.concat(r,'":'),n)}))},[f.formState.isValid,f.formState.errors]);let b=(0,l.mN)({resolver:(0,d.u)(c.gS),defaultValues:{_id:(null==r?void 0:r._id)||"",password:"",confirmPassword:""}});return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.lV,{...f,children:(0,t.jsxs)("form",{onSubmit:f.handleSubmit(e=>{var r;console.log("Form submitted with data:",e),console.log("Form errors:",f.formState.errors);let t={...e,phonenumber:(null==(r=e.phonenumber)?void 0:r.toString())||"",bio:e.bio||"",permissions:Array.isArray(e.permissions)?e.permissions:[]};console.log("Transformed data:",t),n(t)},e=>{console.log("Form validation errors:",e)}),className:"px-12 flex-shrink-0 w-full",noValidate:!0,children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-x-4 relative",children:[(0,t.jsx)(o.zB,{control:f.control,name:"username",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"User Name"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)(i.p,{placeholder:"username",...r})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:f.control,name:"email",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Email"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)(i.p,{placeholder:"email",type:"email",...r})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:f.control,name:"phonenumber",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Số điện thoại"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)(i.p,{placeholder:"Số điện thoại",type:"text",...r})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:f.control,name:"gender",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Giới T\xednh"}),(0,t.jsx)(o.MJ,{children:(0,t.jsxs)("select",{...r,className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,t.jsx)("option",{value:"",children:"Chọn giới t\xednh"}),y.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:f.control,name:"bio",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Tiểu sử"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)("textarea",{...r,rows:3,className:"w-full p-2 border border-gray-300 rounded-md"})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:f.control,name:"private",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Kho\xe1 Th\xe0nh vi\xean"}),(0,t.jsx)(o.MJ,{children:(0,t.jsxs)("div",{className:"flex flex-col space-y-3 mt-2",children:[(0,t.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"private-".concat(r.name),className:"w-4 h-4 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500 focus:ring-2 mr-3",value:"true",checked:!0===r.value,onChange:()=>r.onChange(!0)}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900",children:"Kho\xe1 Kh\xe1ch h\xe0ng"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản sẽ bị v\xf4 hiệu h\xf3a"})]})]}),(0,t.jsxs)("label",{className:"flex items-center cursor-pointer p-3 border rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"private-".concat(r.name),className:"w-4 h-4 text-green-600 bg-gray-100 border-gray-300 focus:ring-green-500 focus:ring-2 mr-3",value:"false",checked:!1===r.value,onChange:()=>r.onChange(!1)}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900",children:"Hoạt động"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"T\xe0i khoản hoạt động b\xecnh thường"})]})]})]})}),(0,t.jsx)(o.C5,{})]})}})]}),(0,t.jsx)("div",{className:"mt-2 text-red-500 text-sm font-medium",children:p}),(0,t.jsx)("div",{className:"flex gap-4 justify-center mt-6",children:(0,t.jsxs)("button",{disabled:!!m,type:"submit",onClick:()=>{console.log("Submit button clicked"),console.log("Loading state:",m),console.log("Form valid:",f.formState.isValid),console.log("Form errors:",f.formState.errors)},className:"btn btn-primary bg-blue-700 w-40 text-white flex items-center",children:[m?(0,t.jsx)(a.A,{className:"animate-spin"}):"","X\xe1c Nhận"]})})]})}),(0,t.jsx)(o.lV,{...b,children:(0,t.jsxs)("form",{onSubmit:b.handleSubmit(u),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto mt-8",noValidate:!0,children:[(0,t.jsx)(o.zB,{control:b.control,name:"password",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"Mật khẩu mới"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)(i.p,{placeholder:"password",type:"password",...r})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsx)(o.zB,{control:b.control,name:"confirmPassword",render:e=>{let{field:r}=e;return(0,t.jsxs)(o.eI,{children:[(0,t.jsx)(o.lR,{children:"X\xe1c nhận mật khẩu"}),(0,t.jsx)(o.MJ,{children:(0,t.jsx)(i.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...r})}),(0,t.jsx)(o.C5,{})]})}}),(0,t.jsxs)("button",{disabled:!!m,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[m?(0,t.jsx)(a.A,{className:"animate-spin"}):"","Update Password"]})]})})]})};var m=n(78296),g=n(74744),p=n(52619),x=n.n(p),y=n(48203),f=n(11925),b=n(85749),v=n(12758),j=n.n(v);function w(){return(w=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}var C=(0,s.forwardRef)(function(e,r){var n=e.color,t=e.size,o=void 0===t?24:t,i=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return s.createElement("svg",w({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("circle",{cx:"12",cy:"12",r:"10"}),s.createElement("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),s.createElement("line",{x1:"9",y1:"9",x2:"15",y2:"15"}))});function O(){return(O=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}C.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},C.displayName="XCircle";var k=(0,s.forwardRef)(function(e,r){var n=e.color,t=e.size,o=void 0===t?24:t,i=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return s.createElement("svg",O({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("path",{d:"M12 20h9"}),s.createElement("path",{d:"M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"}))});function N(){return(N=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}k.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},k.displayName="Edit3";var _=(0,s.forwardRef)(function(e,r){var n=e.color,t=e.size,o=void 0===t?24:t,i=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return s.createElement("svg",N({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},i),s.createElement("polyline",{points:"20 6 9 17 4 12"}))});_.propTypes={color:j().string,size:j().oneOfType([j().string,j().number])},_.displayName="Check";var S=n(72544),Q=n(34502);function E(e){let{user:r,onPermissionsUpdate:n}=e,[o,i]=(0,s.useState)(!1),[a,l]=(0,s.useState)(r.permissions||[]),c=r.permissions||[],d="admin"===r.rule,h=Q.v_.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(f.A,{className:"text-blue-500",size:24}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Quyền truy cập"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:d?"Quản trị vi\xean c\xf3 tất cả quyền":"".concat(c.length," quyền được cấp")})]})]}),!d&&(0,t.jsx)("div",{className:"flex space-x-2",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>{n&&n(r._id,a),i(!1)},className:"flex items-center space-x-1 px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm",children:[(0,t.jsx)(b.A,{size:16}),(0,t.jsx)("span",{children:"Lưu"})]}),(0,t.jsxs)("button",{onClick:()=>{l(r.permissions||[]),i(!1)},className:"flex items-center space-x-1 px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm",children:[(0,t.jsx)(C,{size:16}),(0,t.jsx)("span",{children:"Hủy"})]})]}):(0,t.jsxs)("button",{onClick:()=>i(!0),className:"flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm",children:[(0,t.jsx)(k,{size:16}),(0,t.jsx)("span",{children:"Chỉnh sửa"})]})})]}),d?(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"text-blue-600",size:20}),(0,t.jsx)("span",{className:"text-blue-800 font-medium",children:"Quản trị vi\xean c\xf3 quyền truy cập tất cả c\xe1c t\xednh năng"})]})}):(0,t.jsx)("div",{className:"space-y-6",children:Object.entries(h).map(e=>{let[r,n]=e;return(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:r}),(0,t.jsx)("div",{className:"space-y-2",children:n.map(e=>{let r=o?a.includes(e.id):c.includes(e.id);return(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-1",children:o?(0,t.jsx)("button",{onClick:()=>{var r;return r=e.id,void(a.includes(r)?l(a.filter(e=>e!==r)):l([...a,r]))},className:"w-5 h-5 rounded border-2 flex items-center justify-center ".concat(r?"bg-green-500 border-green-500 text-white":"border-gray-300 hover:border-green-400"),children:r&&(0,t.jsx)(_,{size:12})}):(0,t.jsx)("div",{className:"w-5 h-5 rounded border-2 flex items-center justify-center ".concat(r?"bg-green-500 border-green-500 text-white":"bg-red-100 border-red-300 text-red-500"),children:r?(0,t.jsx)(_,{size:12}):(0,t.jsx)(S.A,{size:12})})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"font-medium ".concat(r?"text-gray-900":"text-gray-500"),children:e.name}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat(r?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:r?"C\xf3 quyền":"Kh\xf4ng c\xf3 quyền"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description})]})]},e.id)})})]},r)})})]})}function z(e){let{params:r}=e,[n,o]=(0,s.useState)(null),i=(0,s.use)(r).id;(0,s.useEffect)(()=>{let e=new AbortController,{signal:r}=e,n=async()=>{try{let n=localStorage.getItem("sessionToken")||"";console.log("Fetching user with ID:",i),console.log("Session token exists:",!!n);let t=await m.A.fetchUserById(i,n,r);if(!r.aborted)if(console.log("User fetch result:",t),t.payload.success)o(t.payload.user);else{var e;console.error("Error fetching user:",t.payload),g.oR.error("Failed to fetch user data: "+((null==(e=t.payload)?void 0:e.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){r.aborted||(console.error("Unexpected error:",e),g.oR.error("An error occurred while fetching user data"))}};return i&&n(),()=>{e.abort()}},[i]);let a=async e=>{try{console.log("Submitting user update data:",e);let n=localStorage.getItem("sessionToken")||"",t=await m.A.updateUser(e,n);if(console.log("Update result:",t),t.payload.success)o(t.payload.user),g.oR.success("Cập nhật th\xe0nh c\xf4ng!");else{var r;console.error("Error updating user:",t.payload),g.oR.error("Kh\xf4ng thể cập nhật: "+((null==(r=t.payload)?void 0:r.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),g.oR.error("C\xf3 lỗi xảy ra khi cập nhật. Vui l\xf2ng thử lại.")}},l=async e=>{try{console.log("Submitting password change data:",e);let n=localStorage.getItem("sessionToken")||"",t=await m.A.updatePassUser(e,n);if(console.log("Password change result:",t),t.payload.success)g.oR.success("Đổi mật khẩu th\xe0nh c\xf4ng!");else{var r;console.error("Error changing password:",t.payload),g.oR.error("Kh\xf4ng thể đổi mật khẩu: "+((null==(r=t.payload)?void 0:r.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),g.oR.error("C\xf3 lỗi xảy ra khi đổi mật khẩu. Vui l\xf2ng thử lại.")}},c=async(e,r)=>{try{let e=localStorage.getItem("sessionToken")||"",s={...n,permissions:r},i=await m.A.updateUser(s,e);if(i.payload.success)o(i.payload.user),g.oR.success("Cập nhật quyền th\xe0nh c\xf4ng!");else{var t;console.error("Error updating permissions:",i.payload),g.oR.error("Kh\xf4ng thể cập nhật quyền: "+((null==(t=i.payload)?void 0:t.message)||"Lỗi kh\xf4ng x\xe1c định"))}}catch(e){console.error("Unexpected error:",e),g.oR.error("C\xf3 lỗi xảy ra khi cập nhật quyền. Vui l\xf2ng thử lại.")}};return(0,t.jsx)(y.default,{requiredPermission:"user_edit",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Chỉnh sửa t\xe0i khoản"}),(0,t.jsx)(x(),{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",href:"/dashboard/user/log/".concat(null==n?void 0:n._id),children:"Xem User log"})]}),n?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,t.jsx)(u,{onSubmit:a,onSubmitPass:l,user:n})]}),(0,t.jsx)(E,{user:n,onPermissionsUpdate:c})]}):(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-500",children:"Đang tải th\xf4ng tin..."})]})})]})})}},58870:(e,r,n)=>{Promise.resolve().then(n.bind(n,38867))},72544:(e,r,n)=>{"use strict";n.d(r,{A:()=>l});var t=n(12115),s=n(12758),o=n.n(s);function i(){return(i=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}var a=(0,t.forwardRef)(function(e,r){var n=e.color,s=e.size,o=void 0===s?24:s,a=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return t.createElement("svg",i({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),t.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),t.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))});a.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},a.displayName="X";let l=a},85749:(e,r,n)=>{"use strict";n.d(r,{A:()=>l});var t=n(12115),s=n(12758),o=n.n(s);function i(){return(i=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}).apply(this,arguments)}var a=(0,t.forwardRef)(function(e,r){var n=e.color,s=e.size,o=void 0===s?24:s,a=function(e,r){if(null==e)return{};var n,t,s=function(e,r){if(null==e)return{};var n,t,s={},o=Object.keys(e);for(t=0;t<o.length;t++)n=o[t],r.indexOf(n)>=0||(s[n]=e[n]);return s}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(t=0;t<o.length;t++)n=o[t],!(r.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,["color","size"]);return t.createElement("svg",i({ref:r,xmlns:"http://www.w3.org/2000/svg",width:o,height:o,viewBox:"0 0 24 24",fill:"none",stroke:void 0===n?"currentColor":n,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},a),t.createElement("path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"}),t.createElement("polyline",{points:"17 21 17 13 7 13 7 21"}),t.createElement("polyline",{points:"7 3 7 8 15 8"}))});a.propTypes={color:o().string,size:o().oneOfType([o().string,o().number])},a.displayName="Save";let l=a}},e=>{e.O(0,[9268,2739,4744,7325,2619,3078,8441,1255,7358],()=>e(e.s=58870)),_N_E=e.O()}]);