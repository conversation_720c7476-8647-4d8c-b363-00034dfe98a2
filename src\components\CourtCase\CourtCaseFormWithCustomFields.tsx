"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  CourtCaseSchema,
  CourtCaseType,
  CourtCaseItemType
} from "@/schemaValidations/courtCase.schema";
import { toast } from "react-toastify";
import customFieldApiRequest, { CustomField } from "@/apiRequests/customField";
import { formatDate } from "@/utils/formatters";

interface CourtCaseFormWithCustomFieldsProps {
  courtCase?: CourtCaseItemType | null;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  loading?: boolean;
}

const CourtCaseFormWithCustomFields: React.FC<CourtCaseFormWithCustomFieldsProps> = ({
  courtCase,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const isEdit = !!courtCase;
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, any>>({});
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CourtCaseType>({
    resolver: zodResolver(CourtCaseSchema),
    defaultValues: {}
  });

  useEffect(() => {
    fetchCustomFields();
  }, []);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onCancel();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [onCancel]);



  useEffect(() => {
    if (courtCase) {
      reset({
        ...courtCase
      });

      // Set custom field values with proper date formatting
      if (courtCase.customFields) {
        const formattedCustomFields = { ...courtCase.customFields };

        // Format custom date fields
        customFields.forEach(field => {
          if (field.dataType === 'date' && formattedCustomFields[field.name]) {
            // Keep dd/mm/yyyy format for custom fields
            if (!formattedCustomFields[field.name].match(/^\d{2}\/\d{2}\/\d{4}$/)) {
              formattedCustomFields[field.name] = formatDate(formattedCustomFields[field.name]);
            }
          }
        });

        setCustomFieldValues(formattedCustomFields);
      }
    }
  }, [courtCase, reset, customFields]);

  const fetchCustomFields = async () => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await customFieldApiRequest.getCustomFields('CourtCase', sessionToken);
      
      if (result.payload.success) {
        setCustomFields(result.payload.fields);
      }
    } catch (error) {
      console.error("Error fetching custom fields:", error);
    }
  };

  const handleCustomFieldChange = (fieldName: string, value: any) => {
    setCustomFieldValues(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const onFormSubmit = (data: CourtCaseType) => {
    // Validate custom fields
    const customFieldErrors: string[] = [];

    // Note: Validation will be handled on the backend
    // Frontend validation can be added here if needed

    if (customFieldErrors.length > 0) {
      customFieldErrors.forEach(error => toast.error(error));
      return;
    }

    // Simplified data handling - no date formatting needed
    const formattedData = {
      ...data
    };

    // Combine regular data with custom fields
    const submitData = {
      ...formattedData,
      customFields: customFieldValues
    };

    onSubmit(submitData);
  };

  const renderCustomField = (field: CustomField) => {
    const value = customFieldValues[field.name] || field.config.defaultValue || '';
    const fieldId = `custom_${field.name}`;

    switch (field.dataType) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
        return (
          <input
            id={fieldId}
            type={field.dataType === 'email' ? 'email' : field.dataType === 'phone' ? 'tel' : field.dataType === 'url' ? 'url' : 'text'}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={field.description || `Nhập ${field.label.toLowerCase()}`}
            required={field.config.required}
            maxLength={field.config.maxLength}
            minLength={field.config.minLength}
            pattern={field.config.pattern}
          />
        );

      case 'textarea':
        return (
          <textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.name, e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={field.description || `Nhập ${field.label.toLowerCase()}`}
            required={field.config.required}
            maxLength={field.config.maxLength}
            minLength={field.config.minLength}
          />
        );

      case 'number':
      case 'currency':
      case 'percentage':
        return (
          <input
            id={fieldId}
            type="number"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.name, parseFloat(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={field.description || `Nhập ${field.label.toLowerCase()}`}
            required={field.config.required}
            min={field.config.min}
            max={field.config.max}
            step={field.config.decimals ? `0.${'0'.repeat(field.config.decimals - 1)}1` : '1'}
          />
        );

      case 'date':
        // Convert dd/mm/yyyy to yyyy-mm-dd for input
        const formatDateForInput = (dateStr: string): string => {
          if (!dateStr) return '';
          if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) return dateStr;
          if (dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
            const [day, month, year] = dateStr.split('/');
            return `${year}-${month}-${day}`;
          }
          return new Date(dateStr).toISOString().split('T')[0];
        };

        // Convert yyyy-mm-dd to dd/mm/yyyy for storage
        const formatDateForStorage = (dateStr: string): string => {
          if (!dateStr) return '';
          if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateStr.split('-');
            return `${day}/${month}/${year}`;
          }
          return dateStr;
        };

        return (
          <div className="relative">
            <input
              id={fieldId}
              type="date"
              value={formatDateForInput(value)}
              onChange={(e) => handleCustomFieldChange(field.name, formatDateForStorage(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 date-input-custom"
              required={field.config.required}
              min={field.config.minDate ? formatDateForInput(field.config.minDate) : undefined}
              max={field.config.maxDate ? formatDateForInput(field.config.maxDate) : undefined}
              style={{
                colorScheme: 'light',
                position: 'relative'
              }}
            />
            {!formatDateForInput(value) && (
              <div
                className="absolute left-3 top-2.5 text-gray-400 text-sm pointer-events-none select-none"
                style={{
                  zIndex: 1
                }}
              >
                dd/mm/yyyy
              </div>
            )}
            <style jsx>{`
              .date-input-custom::-webkit-datetime-edit-text,
              .date-input-custom::-webkit-datetime-edit-month-field,
              .date-input-custom::-webkit-datetime-edit-day-field,
              .date-input-custom::-webkit-datetime-edit-year-field {
                color: transparent;
              }

              .date-input-custom:focus::-webkit-datetime-edit-text,
              .date-input-custom:focus::-webkit-datetime-edit-month-field,
              .date-input-custom:focus::-webkit-datetime-edit-day-field,
              .date-input-custom:focus::-webkit-datetime-edit-year-field {
                color: #374151;
              }

              .date-input-custom:not(:placeholder-shown)::-webkit-datetime-edit-text,
              .date-input-custom:not(:placeholder-shown)::-webkit-datetime-edit-month-field,
              .date-input-custom:not(:placeholder-shown)::-webkit-datetime-edit-day-field,
              .date-input-custom:not(:placeholder-shown)::-webkit-datetime-edit-year-field {
                color: #374151;
              }

              .date-input-custom[value]::-webkit-datetime-edit-text,
              .date-input-custom[value]::-webkit-datetime-edit-month-field,
              .date-input-custom[value]::-webkit-datetime-edit-day-field,
              .date-input-custom[value]::-webkit-datetime-edit-year-field {
                color: #374151;
              }
            `}</style>
          </div>
        );

      case 'datetime':
        return (
          <input
            id={fieldId}
            type="datetime-local"
            value={value ? new Date(value).toISOString().slice(0, 16) : ''}
            onChange={(e) => handleCustomFieldChange(field.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required={field.config.required}
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name={fieldId}
                checked={value === true}
                onChange={() => handleCustomFieldChange(field.name, true)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span>Có</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name={fieldId}
                checked={value === false}
                onChange={() => handleCustomFieldChange(field.name, false)}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span>Không</span>
            </label>
          </div>
        );

      case 'select':
        return (
          <select
            id={fieldId}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required={field.config.required}
          >
            <option value="">-- Chọn {field.label.toLowerCase()} --</option>
            {field.config.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
            {field.config.options?.map((option) => (
              <label key={option.value} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={Array.isArray(value) && value.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(value) ? value : [];
                    if (e.target.checked) {
                      handleCustomFieldChange(field.name, [...currentValues, option.value]);
                    } else {
                      handleCustomFieldChange(field.name, currentValues.filter(v => v !== option.value));
                    }
                  }}
                  className="text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm">{option.label}</span>
              </label>
            ))}
          </div>
        );

      default:
        return (
          <input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.name, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder={field.description || `Nhập ${field.label.toLowerCase()}`}
            required={field.config.required}
          />
        );
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onCancel();
        }
      }}
    >
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden relative">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              {isEdit ? '✏️' : '➕'}
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEdit ? 'Chỉnh sửa vụ việc' : 'Thêm vụ việc mới'}
              </h2>
              <p className="text-sm text-gray-600">
                {isEdit ? 'Cập nhật vụ việc (chỉ trường tùy chỉnh)' : `Tạo vụ việc mới với STT & Số Hồ Sơ tự động (${customFields.length} trường tùy chỉnh)`}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            disabled={loading}
            className="text-gray-500 hover:text-gray-700 transition-colors p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50"
          >
            ✕
          </button>
        </div>

        {/* Form Content */}
        <div className="overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]">

          <form id="court-case-form" onSubmit={handleSubmit(onFormSubmit)} className="p-6 space-y-8">


            {/* Custom Fields */}
            {customFields.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4 border-b border-gray-200 pb-2">
                  ⚙️ Thông tin bổ sung
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {customFields.map((field) => (
                    <div key={field._id}>
                      <label htmlFor={`custom_${field.name}`} className="block text-sm font-medium text-gray-700 mb-2">
                        {field.label}
                        {field.config.required && <span className="text-red-500 ml-1">*</span>}
                      </label>
                      {renderCustomField(field)}
                      {field.description && (
                        <p className="mt-1 text-xs text-gray-500">{field.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-end gap-4 p-6 border-t border-gray-200 bg-gray-50">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Hủy
          </button>
          <button
            type="submit"
            form="court-case-form"
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Đang xử lý...' : (isEdit ? 'Cập nhật' : 'Tạo mới')}
          </button>
        </div>

        {/* Loading Overlay */}
        {loading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
            <div className="flex flex-col items-center gap-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="text-sm text-gray-600">
                {isEdit ? 'Đang cập nhật...' : 'Đang tạo vụ việc...'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourtCaseFormWithCustomFields;
